package config

const (
	INIT_EMPTY_FISH_WEIGHT   = 10000 // 默认空鱼权重
	HOOK_FISH_REQ_TIME_DELAY = 300   // 中鱼通讯延时时间(毫秒)
	HOOK_FISH_REQ_INTERVAL   = 10000 // 默认中鱼请求间隔(毫秒)

	// 这里是鱼计算鱼重量使用的参数 别问我为啥这么定 我也不知道！
	FISH_WEIGHT_RAND_MIN = 0.9 // 鱼重量权重最小随机数
	FISH_WEIGHT_RAND_MAX = 1.1 // 鱼重量权重最大随机数
	// 所有鱼的权重都为0的默认值
	// 这个时间内继续计算 10min(秒)
	FISH_WEIGHT_EMPTY_MAX_TIME = 600 // 10min(秒)
	// 超过上诉时间后 请求中鱼时间间隔 10min(秒)
	FISH_WEIGHT_EMPTY_INTERVAL = 600 * 1000 // 10min(毫秒)
)
