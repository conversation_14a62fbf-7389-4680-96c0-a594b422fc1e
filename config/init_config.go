package config

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/config"
)

// InitConfig 初始化配置
func InitConfig() error {
	serviceConfig := config.NewServiceConfig()
	
	serviceConfig.Register("InitHookConstCfg", cmodel.InitHookConstCfg)                    // 中鱼静态配置
	serviceConfig.Register("InitFishPondListCfg", cmodel.InitFishPondListCfg)              // 钓场配置
	serviceConfig.Register("InitFishStockCfg", cmodel.InitFishStockCfg)                    // 钓场投鱼配置
	serviceConfig.Register("InitFishReleaseCfg", cmodel.InitFishReleaseCfg)                // 投鱼详情配置
	serviceConfig.Register("InitBasicFishQualityCfg", cmodel.InitBasicFishQualityCfg)      // 初始化鱼信息配置表
	serviceConfig.Register("InitFishEnvAffinityCfg", cmodel.InitFishEnvAffinityCfg)        // 鱼环境系数配置
	serviceConfig.Register("InitBaitAffinityCfg", cmodel.InitBaitAffinityCfg)              // 鱼饵亲和系数配置表
	serviceConfig.Register("InitBaitTypeAffinityCfg", cmodel.InitBaitTypeAffinityCfg)      // 鱼饵类型亲和系数配置
	serviceConfig.Register("InitTempAffinityCfg", cmodel.InitTempAffinityCfg)              // 温度亲和系数配置
	serviceConfig.Register("InitStructAffinityCfg", cmodel.InitStructAffinityCfg)          // 鱼习性-水下结构体亲和系数配置
	serviceConfig.Register("InitWaterLayerAffinityCfg", cmodel.InitWaterLayerAffinityCfg)  // 鱼习性-水层亲和系数配置
	serviceConfig.Register("InitEnvAffinityConstCfg", cmodel.InitEnvAffinityConstCfg)      // 鱼习性-静态配置
	serviceConfig.Register("InitPoseAffinityCfg", cmodel.InitPoseAffinityCfg)              // 鱼饵姿态系数配置
	serviceConfig.Register("InitItemCfg", cmodel.InitItemCfg)                              // 道具配置
	serviceConfig.Register("InitLuresCfg", cmodel.InitLuresCfg)                            // 拟饵配置
	serviceConfig.Register("InitBaitsCfg", cmodel.InitBaitsCfg)                            // 真饵配置
	serviceConfig.Register("InitBasicFishSpeciesCfg", cmodel.InitBasicFishSpeciesCfg)      // 初始化鱼种信息配置
	serviceConfig.Register("InitBasicFishCharacterCfg", cmodel.InitBasicFishCharacterCfg)  // 初始化鱼特性配置
	serviceConfig.Register("InitLightAffinityCfg", cmodel.InitLightAffinityCfg)            // 初始化鱼光照系数配置
	serviceConfig.Register("InitPeriodAffinityCfg", cmodel.InitPeriodAffinityCfg)          // 初始化鱼时段系数配置
	serviceConfig.Register("InitBasicFishPeriodCfg", cmodel.InitBasicFishPeriodCfg)        // 初始化时间段配置
	serviceConfig.Register("InitStockReleaseCfg", cmodel.InitStockReleaseCfg)              // 钓场投放鱼表配置

	return serviceConfig.ExecuteAll()
}
