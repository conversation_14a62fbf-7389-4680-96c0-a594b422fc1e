package main

import (
	"context"
	"hook2srv/config"
	"hook2srv/internal/proc"
	"hook2srv/internal/server/rpc"
	calcHook "hook2srv/tool/calc_hook"
	"runtime"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/kit/ginx"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/gin-contrib/pprof"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type hook2Service struct {
	Name string
	Ctx  context.Context
}

func (h *hook2Service) Init() error {
	h.Ctx = context.Background()
	h.Name = viper.GetString(dict.ConfigRpcServerName)
	logrus.Infoln(h.Name + "服务Init")

	// TODO 实现各模块初始化操作

	// 初始化配置
	err := config.InitConfig()
	if err != nil {
		logrus.Errorf("初始化配置失败:%+v", err)
		return err
	}

	// 初始化rpc 接口
	rpc.InitHookRpc()

	// 注册GM接口
	proc.InitGmRpc()

	// 中鱼计算工具
	calcHook.ToolCalcHookDeal()

	// pprof 监控注册
	router := ginx.GetGinEngine()
	pprof.Register(router)

	return nil
}

func (s *hook2Service) Start() error {
	// TODO 这里实现启动逻辑
	logrus.Infoln(s.Name + "服务启动成功")
	return nil
}

func (s *hook2Service) Stop() error {
	// TODO 这里实现服务正常关闭逻辑
	logrus.Infoln(s.Name + "服务关闭中...")
	return nil
}

func (s *hook2Service) ForceStop() error {
	// TODO 这里实现强制关闭逻辑
	logrus.Infoln(s.Name + " ForceStop ...")
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource() // 初始化随机数
	driver.Run(&hook2Service{})
}
