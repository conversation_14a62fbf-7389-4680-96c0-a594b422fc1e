# server
rpc_server_name: hook2
rpc_port: 12601

# 端口号
http_port: 22601

# 日志相关
log_level: debug
log_write: true
log_dir: ../../logs/hook2
log_json: false
log_kafka_enable: true

redis_addr: 192.168.1.58:6379
redis_passwd: 8888

redis_list:
  gateway:
    addr: 192.168.1.58:6379
    passwd: 8888

  player:
    addr: 192.168.1.58:6379
    passwd: 8888

  game:
    addr: 192.168.1.58:6379
    passwd: 8888

consul_addr: 192.168.1.58:8500

nsqd_addr: 192.168.1.58:4150
nsqd_http_addr: 192.168.1.58:4151
nsqlookupd_addrs:
  - 192.168.1.58:4161

rpc_server_tags: normal

kafka-producer:
  brokers: ["192.168.1.58:9092"]
  timeout: 10
