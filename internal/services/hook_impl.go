package services

import (
	"context"
	"sync"

	"hook2srv/internal/logic"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hookRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hookrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"github.com/sirupsen/logrus"
)

type HookService struct {
}

var (
	onceHook = &sync.Once{}
	instance *HookService
)

func GetHookServiceInstance() *HookService {
	if instance != nil {
		return instance
	}

	onceHook.Do(func() {
		instance = &HookService{}
	})
	return instance
}

// ThrowRod 抛竿请求
func (h *HookService) ThrowRod(ctx context.Context, req *hookRpc.ThrowRodRouteReq) (*hookRpc.ThrowRodRouteRsp, error) {

	rsp := &hookRpc.ThrowRodRouteRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)}
	entry := logx.NewLogEntry(ctx)

	// 调用逻辑接口处理
	conInfo, err := logic.PlayerThrowRod(ctx, req.GetPlayerId(), req)
	if err != nil {
		entry.Errorf("ThrowRod req:%s, err:%+v", req.String(), err)
		rsp.Ret = protox.FillErrResult(err)
		rsp.Ret.Desc = "deal logic error"
		return rsp, nil
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.SyncControl = conInfo

	logrus.Debugf("[ThrowRod]:req:%s, rsp:%s", req.String(), rsp.String())

	return rsp, nil
}

// FishHook 中鱼请求
func (h *HookService) FishHook(ctx context.Context, req *hookRpc.FishHookRouteReq) (*hookRpc.FishHookRouteRsp, error) {
	rsp := &hookRpc.FishHookRouteRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)}
	entry := logx.NewLogEntry(ctx)

	hookHabit := req.GetHookHabit()
	if hookHabit == nil {
		entry.Errorf("FishHook, red:%s, hookHabit empty", req.String())
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM, "hook habit empty")
		return rsp, nil
	}

	// 调用逻辑接口处理
	fishInfo, fakeFishId, nextReqTime, err := logic.CheckPlayerHookFish(ctx, req.GetPlayerId(), req.GetPondId(), req.GetRigId(), hookHabit)
	if err != nil {
		entry.Errorf("FishHook, req:%s, err:%+v", req.String(), err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp, nil
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.FishInfo = fishInfo
	rsp.NextReqTime = nextReqTime
	rsp.FakeFishId = fakeFishId

	entry.Debugf("[FishHook]:req:%s, rsp:%s", req.String(), rsp.String())

	return rsp, nil
}

// BattleFish 搏鱼请求
func (h *HookService) BattleFish(ctx context.Context, req *hookRpc.FishBattleRouteReq) (*hookRpc.FishBattleRouteRsp, error) {
	entry := logx.NewLogEntry(ctx)
	rsp := &hookRpc.FishBattleRouteRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)}

	// 调用逻辑接口处理
	fishData, err := logic.PlayerBattleFish(ctx, req.GetPlayerId(), req.GetRigId(), req.GetFishResult())
	if err != nil || fishData == nil {
		entry.Errorf("BattleFish req:%s, err:%+v", req.String(), err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp, nil
	}

	rsp.FishInfo = fishData.ToProto(ctx)
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("[BattleFish]:req:%s, rsp:%s", req.String(), rsp.String())

	return rsp, nil
}

// CatchRod 收杆请求
func (h *HookService) CatchRod(ctx context.Context, req *hookRpc.CatchRodRouteReq) (*hookRpc.CatchRodRouteRsp, error) {
	rsp := &hookRpc.CatchRodRouteRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)}
	entry := logx.NewLogEntry(ctx)

	hookHabit := req.GetHookHabit()
	if hookHabit == nil {
		entry.Errorf("FishHook, req:%s, hookHabit empty", req.String())
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM, "hook habit empty")
		return rsp, nil
	}

	// 调用逻辑接口处理
	hookBait, fishStatus, fishInfo := logic.PlayerCatchRod(ctx, req.GetPlayerId(), req.GetRigId(), hookHabit)

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.FishResult = fishStatus
	rsp.FishInfo = fishInfo.ToProto(ctx)
	rsp.HookBait = hookBait

	entry.Debugf("[CatchRod]:req:%s, rsp:%s", req.String(), rsp.String())

	return rsp, nil
}

// HookStart 开始中鱼请求
func (h *HookService) HookStart(ctx context.Context, req *hookRpc.HookStartRouteReq) (*hookRpc.HookStartRouteRsp, error) {
	rsp := &hookRpc.HookStartRouteRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)}
	entry := logx.NewLogEntry(ctx)

	// 调用逻辑接口处理
	conInfo, err := logic.HookFishStart(ctx, req.GetPlayerId(), req)
	if err != nil {
		entry.Errorf("HookStart req:%s, err:%+v", req.String(), err)
		rsp.Ret = protox.FillErrResult(err)
		rsp.Ret.Desc = "deal logic error"
		return rsp, nil
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.SyncControl = conInfo

	logrus.Debugf("[HookStart]:req:%s, rsp:%s", req.String(), rsp.String())

	return rsp, nil
}
