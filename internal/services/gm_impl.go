package services

import (
	"context"
	"sync"

	daoHook "hook2srv/internal/dao/dao_hook"
	modelHook "hook2srv/internal/model/model_hook"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gmPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gm"
	gmRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/gmrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"google.golang.org/protobuf/encoding/protojson"
)

type GmService struct{}

var (
	gmOnce              = &sync.Once{}
	gmSingletonInstance *GmService
)

func GetGmInstance() *GmService {
	if gmSingletonInstance != nil {
		return gmSingletonInstance
	}
	gmOnce.Do(func() {
		gmSingletonInstance = &GmService{}
	})
	return gmSingletonInstance
}

// OperatorHookFish 中鱼操作
func (g *GmService) OperatorHookFish(ctx context.Context, req *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("[gm:OperatorHookFish]:%s", req.String())
	rsp := &gmRpc.GmCmdRsp{
		Ret: protox.DefaultResult(),
	}

	reqd := &gmPB.GmCmdOperateHookFishReq{}
	err := protojson.Unmarshal([]byte(req.Data), reqd)
	if err != nil {
		return rsp, err
	}

	if reqd.GetPlayerId() <= 0 || (reqd.GetFishId() <= 0 && reqd.GetOperate() == 1) {
		entry.Errorf("[gm:OperatorHookFish] invalid param:%s", req.String())
		return rsp, nil
	}

	// 1:修改 2:删除 3:随机一条配置的鱼(不按概率计算) 4:查询
	optType := reqd.GetOperate()
	fixFish := &modelHook.FixHookFish{}
	switch optType {
	case 1:
		// 修改
		err = daoHook.UpdatePlayerFixHookFish(ctx, reqd.GetPlayerId(), reqd.GetFishId(), false)
	case 2:
		// 删除
		err = daoHook.DeletePlayerFixHookFish(ctx, reqd.GetPlayerId())
	case 3:
		// 随机一条配置的鱼(不按概率计算)
		err = daoHook.UpdatePlayerFixHookFish(ctx, reqd.GetPlayerId(), reqd.GetFishId(), true)
	case 4:
		// 查询
		fixFish, err = daoHook.QueryPlayerFixHookFish(ctx, reqd.GetPlayerId())
	default:
		entry.Errorf("[gm:OperatorHookFish] invalid operate:%d", optType)
		return rsp, nil
	}

	if err != nil {
		entry.Errorf("[gm:OperatorHookFish] operate:%d error:%v", optType, err)
		return rsp, err
	}

	// 查询结果
	if optType == 4 {
		rsp.Data = fixFish.ToJsonStr()
	} else {
		rsp.Data = "success"
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("[gm:OperatorHookFish]:req:%s, rsp:%s", req.String(), rsp.String())

	return rsp, nil
}