package rpc

import (
	"context"
	"hook2srv/internal/services"

	"git.keepfancy.xyz/back-end/frameworks/kit/rpc"

	hookRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hookrpc"
)

type HookServiceServer struct {
}

// GetThrowRodReq 抛竿请求
func (h *HookServiceServer) GetThrowRodReq(ctx context.Context, req *hookRpc.ThrowRodRouteReq) (*hookRpc.ThrowRodRouteRsp, error) {
	return services.GetHookServiceInstance().ThrowRod(ctx, req)
}

// GetFishHookReq 中鱼请求
func (h *HookServiceServer) GetFishHookReq(ctx context.Context, req *hookRpc.FishHookRouteReq) (*hookRpc.FishHookRouteRsp, error) {
	return services.GetHookServiceInstance().FishHook(ctx, req)
}

// GetCatchRodReq 收杆请求
func (h *HookServiceServer) GetCatchRodReq(ctx context.Context, req *hookRpc.CatchRodRouteReq) (*hookRpc.CatchRodRouteRsp, error) {
	return services.GetHookServiceInstance().CatchRod(ctx, req)
}

// GetFishBattleReq 搏鱼请求
func (h *HookServiceServer) GetFishBattleReq(ctx context.Context, req *hookRpc.FishBattleRouteReq) (*hookRpc.FishBattleRouteRsp, error) {
	return services.GetHookServiceInstance().BattleFish(ctx, req)
}

// GetHookStartReq 中鱼开始请求
func (h *HookServiceServer) GetHookStartReq(ctx context.Context, req *hookRpc.HookStartRouteReq) (*hookRpc.HookStartRouteRsp, error) {
	return services.GetHookServiceInstance().HookStart(ctx, req)
}

func InitHookRpc() {
	hookRpcService := &HookServiceServer{}
	hookRpc.RegisterHookServiceServer(rpc.Server, hookRpcService)
}
