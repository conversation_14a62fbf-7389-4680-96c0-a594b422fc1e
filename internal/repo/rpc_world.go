package repo

import (
	"context"

	worldRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/worldrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_world"
)

// RpcGetWeather 查询天气
func RpcGetWeather(ctx context.Context, pondId int64) *worldRpc.WeatherRsp {
	entry := logx.NewLogEntry(ctx)

	rpcRsp, err := crpc_world.RpcGetWeather(ctx, pondId)
	if err != nil {
		entry.Errorf("RpcGetWeather err:%v", err)
		return nil
	}

	return rpcRsp
}