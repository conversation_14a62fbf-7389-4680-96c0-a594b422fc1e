package daoHook

import (
	"context"
	"fmt"
	"hook2srv/config"
	modelHook "hook2srv/internal/model/model_hook"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
)

// FmtHookDataRdsKey 玩家中鱼数据redis key 格式化
func FmtHookDataRdsKey(playerId uint64, rigId int32) string {
	return fmt.Sprintf(config.RDS_PLAYER_HOOK_DATA, playerId, rigId)
}

// UpdatePlayerHookData 更新玩家中鱼信息
func UpdatePlayerHookData(ctx context.Context, playerId uint64, hookData *modelHook.HookData) error {
	entry := logx.NewLogEntry(ctx)

	if hookData == nil {
		entry.Errorf("hook data is nil")
		return fmt.Errorf("hook data is nil")
	}

	pipLine := redisx.GetGameCli().TxPipeline()

	rdsKey := FmtHookDataRdsKey(playerId, hookData.RigId)
	// 更新到redis
	pipLine.HSet(ctx, rdsKey, hookData.ToRdsHash())
	pipLine.Expire(ctx, rdsKey, config.HOOK_DATA_EXPIRE)

	_, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("set hook data cache err:%v", err)
		return err
	}

	return nil
}

// QueryPlayerHookData 查询玩家中鱼信息
func QueryPlayerHookData(ctx context.Context, playerId uint64, rigId int32) *modelHook.HookData {
	entry := logx.NewLogEntry(ctx)

	rdsHash, err := redisx.GetGameCli().HGetAll(ctx, FmtHookDataRdsKey(playerId, rigId)).Result()
	if err != nil {
		entry.Errorf("query hook data cache err:%v", err)
		return nil
	}

	if len(rdsHash) == 0 {
		return nil
	}

	hookData := modelHook.NewHookDataFromRdsHash(rdsHash)
	if hookData == nil {
		entry.Errorf("hook data redis data:%+v, to struct failed", rdsHash)
		return nil
	}

	entry.Debugf("query hook data:%+v", hookData)

	return hookData
}
