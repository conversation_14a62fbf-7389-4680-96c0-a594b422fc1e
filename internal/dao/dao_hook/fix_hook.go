package daoHook

import (
	"context"
	"errors"
	"fmt"
	"hook2srv/config"
	modelHook "hook2srv/internal/model/model_hook"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

// FmtFixHookFishRdsKey 玩家固定中鱼redis key 格式化
func FmtFixHookFishRdsKey(playerId uint64) string {
	return fmt.Sprintf(config.RDS_PLAYER_FIX_HOOK_FISH, playerId)
}

// UpdatePlayerFixHookFish 修改固定中鱼信息 (GM使用)
func UpdatePlayerFixHookFish(ctx context.Context, playerId uint64, fishId int64, isRand bool) error {
	entry := logx.NewLogEntry(ctx)
	if fishId <= 0 && !isRand {
		entry.Errorf("update player:%d fix hook fish fail, fishId:%d, isRand:%t", playerId, fishId, isRand)
		return fmt.Errorf("fishId:%d, isRand:%t param error", fishId, isRand)
	}

	// 更新到redis
	fixFish := &modelHook.FixHookFish{
		PlayerId: playerId,
		FishId:   fishId,
		IsRand:   isRand,
	}

	err := redisx.GetGameCli().Set(ctx, FmtFixHookFishRdsKey(playerId), fixFish.ToJsonStr(), config.FIX_HOOK_FISH_EXPIRE).Err()

	return err
}

// QueryPlayerFixHookFish 查询固定中鱼信息
func QueryPlayerFixHookFish(ctx context.Context, playerId uint64) (*modelHook.FixHookFish, error) {
	entry := logx.NewLogEntry(ctx)
	fishData, err := redisx.GetGameCli().Get(ctx, FmtFixHookFishRdsKey(playerId)).Result()

	if errors.Is(err, redis.Nil) {
		return &modelHook.FixHookFish{}, err
	}

	if err != nil {
		entry.Errorf("query player:%d fix hook fish fail, %v", playerId, err)
		return nil, err
	}

	return modelHook.NewFixHookFishFromRdsStr(fishData), err
}

// DeletePlayerFixHookFish 删除固定中鱼信息
func DeletePlayerFixHookFish(ctx context.Context, playerId uint64) error {
	return redisx.GetGameCli().Del(ctx, FmtFixHookFishRdsKey(playerId)).Err()
}
