package logic

import (
	"context"

	"hook2srv/config"
	daoHook "hook2srv/internal/dao/dao_hook"
	logicConf "hook2srv/internal/logic/logic_conf"
	logicFish "hook2srv/internal/logic/logic_fish"
	logicTime "hook2srv/internal/logic/logic_time"
	logicWeight "hook2srv/internal/logic/logic_weight"
	modelHook "hook2srv/internal/model/model_hook"

	hookRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hookrpc"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
)

// PlayerThrowRod 玩家抛竿
func PlayerThrowRod(ctx context.Context, playerId uint64, req *hookRpc.ThrowRodRouteReq) (*commonPB.FishSyncControl, error) {
	//entry := logx.NewLogEntry(ctx)

	rigId := req.GetRigId()
	pondId := req.GetPondId()
	hookHabit := req.GetHookHabit()

	return DealInitHookTime(ctx, playerId, pondId, rigId, req.GetHookBait(), hookHabit, commonPB.HOOK_FISH_CALC_TYPE_HFCT_START)
}

// CheckPlayerHookFish 玩家中鱼检测  返回中鱼信息 假咬口鱼id(针对未中鱼) 下次请求时间 错误信息
func CheckPlayerHookFish(ctx context.Context, playerId uint64, pondId int64, rigId int32, hookHabit *commonPB.HookHabitParam) (*commonPB.FishInfo, int64, int64, error) {
	entry := logx.NewLogEntry(ctx)
	nextReqTime := int64(config.HOOK_FISH_REQ_INTERVAL)

	if hookHabit == nil {
		entry.Errorf("hook habit is nil")
		return nil, 0, nextReqTime, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "habit param empty")
	}

	// 中鱼静态配置
	hookConstConf := cmodel.GetHookConst(consul_config.WithGrpcCtx(ctx))
	if hookConstConf == nil || hookConstConf.ReqFishInterval <= 0 {
		entry.Errorf("hook const conf not exist")
		return nil, 0, nextReqTime, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "hook const conf not exist")
	}

	// 校验参数
	nextReqTime = hookConstConf.ReqFishInterval
	if playerId == 0 || pondId <= 0 || rigId == 0 {
		entry.Errorf("player:%d, pondId:%d, rigId:%d is nil", playerId, pondId, rigId)
		return nil, 0, nextReqTime, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "player pondId or rigId empty")
	}

	// 查询玩家中鱼数据
	hookData := daoHook.QueryPlayerHookData(ctx, playerId, rigId)
	if hookData == nil {
		entry.Errorf("player:%d, rigId:%d hook data not exist", playerId, rigId)
		return nil, 0, nextReqTime, protox.CodeError(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, "query hook data is nil")
	}

	// 0. 检查是否有固定鱼信息
	fishFishInfo := logicFish.GetFixFishInfo(ctx, playerId, pondId, hookData)
	if fishFishInfo != nil {
		daoHook.UpdatePlayerHookData(ctx, playerId, hookData)
		entry.Debugf("player:%d, pondId:%d, rigId:%d has fix fish info:%+v...", playerId, pondId, rigId, fishFishInfo.String())
		return fishFishInfo, 0, 0, nil
	}

	// 1. 小于约定的请求时间(防刷) 直接返回让下次请求
	checkTimeOk := logicTime.CheckReqTimeLimit(hookData, nextReqTime)
	if checkTimeOk {
		entry.Warnf("player:%d, pondId:%d, rigId:%d, hookData:%+v, req time limit...", playerId, pondId, rigId, hookData)
		return nil, 0, nextReqTime, nil
	}

	// 2.计算实鱼权重
	weightMap := logicWeight.CalcPondFishHabitFishWeight(ctx, pondId, hookData.BaitId, hookHabit)
	totalWg := logicWeight.CalcFishTotalWeight(ctx, weightMap)

	// 总权重为0 异常
	if totalWg <= 0 {
		// 抛竿时鱼权重无效
		invalidWgTime := logicTime.CalcInvalidWeightNextTime(hookData, hookConstConf.ReqFishInterval)
		if invalidWgTime > 0 {
			entry.Warnf("pond:%d, rigId:%d, hookData:%+v, nextTime:%d, throw fish weight empty...", pondId, rigId, hookData, invalidWgTime)

			return nil, 0, invalidWgTime, nil
		} else {
			// 抛竿时有效 中鱼无效
			hookData.InvalidWeightTime = timex.Now().Unix()
			daoHook.UpdatePlayerHookData(ctx, playerId, hookData)
			entry.Warnf("pond:%d, rigId:%d, hookData:%+v, hook fish weight empty...", pondId, rigId, hookData)
			return nil, 0, nextReqTime, nil
		}
	} else {
		// 抛竿的时候鱼权重无效 这里计算的时候鱼的权重有效 重新计算空鱼权重和初始时间等
		if hookData.InvalidWeightTime > 0 {
			// 初始化空余权重和时间
			ratio := logicTime.CalcHookUndulateRatio(ctx, pondId, totalWg, 0)
			initTime, emptyWg := logicTime.CalcInitHookFishTime(ratio, totalWg)

			// 更新数据
			hookData.ReloadHookData(emptyWg, initTime)
			daoHook.UpdatePlayerHookData(ctx, playerId, hookData)

			// 下发中鱼请求时间
			nextReqTime = initTime
			if initTime > hookConstConf.ReqFishInterval {
				nextReqTime = hookConstConf.ReqFishInterval
			}

			entry.Debugf("pond:%d, rigId:%d, hookData:%+v, fish weight empty to valid...", pondId, rigId, hookData)

			return nil, 0, nextReqTime, nil
		}
	}

	// 空鱼权重=空鱼权重-间隔时间/ 1000 * 实鱼权重
	emptyWeight := logicTime.CalcNewEmptyWeight(ctx, totalWg, hookData) // hookData.EmptyWeight - int32(timeInterval/1000*int64(totalWg))

	// 新的时间 = 空鱼权重 / 实鱼权重 (毫秒计算)
	newTime := int64(emptyWeight * 1000 / totalWg)

	// 请求时间间隔
	timeInterval := timex.Now().UnixMilli() - hookData.LastTime

	// 先随机一条鱼
	fishId := random.RandForInt64Weight(weightMap)

	// 3. 判断是否中鱼
	if newTime <= 0 || timeInterval >= hookData.LeftMsec-config.HOOK_FISH_REQ_TIME_DELAY {
		if fishId <= 0 {
			entry.Errorf("pond:%d, rigId:%d fish weight error", pondId, rigId)
		}

		// 记录玩家中鱼信息
		hookData.InsertFishData(ctx, fishId, hookData.BaitId)
		// 更新数据
		daoHook.UpdatePlayerHookData(ctx, playerId, hookData)

		entry.Debugf("pondId:%d, rigId:%d, hook fish data:%+v", pondId, rigId, hookData)

		return hookData.GetFishData().ToProto(ctx), 0, 0, nil
	}

	// 4. 在约定期间内 且在中鱼逻辑中 计算下次通讯时间
	if newTime <= nextReqTime {
		nextReqTime = newTime
	}

	// 5. 插入数据
	hookData.ReloadHookData(emptyWeight, newTime)

	// 6. 未中鱼 返回假咬口鱼id
	fakeFishId := fishId

	// 更新数据
	daoHook.UpdatePlayerHookData(ctx, playerId, hookData)

	entry.Debugf("pondId:%d, rigId:%d, hookData:%+v, nextReqTime:%d, hookHabit:%+v, fakeFishId:%d", pondId, rigId, hookData, nextReqTime, hookHabit.String(), fakeFishId)

	return nil, fakeFishId, nextReqTime, nil
}

// PlayerBattleFish 搏鱼请求
func PlayerBattleFish(ctx context.Context, playerId uint64, rigId int32, fishRet commonPB.FISH_RESULT) (*modelHook.FishData, error) {
	entry := logx.NewLogEntry(ctx)

	// 查询玩家中鱼信息
	hookData := daoHook.QueryPlayerHookData(ctx, playerId, rigId)
	if hookData == nil {
		entry.Errorf("player:%d, rigId:%d hook data not exist", playerId, rigId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "habit param empty")
	}

	// 判断是否有鱼
	fishData := hookData.GetFishData()
	if fishData == nil {
		entry.Errorf("player:%d, rigId:%d fish data:%+v error", playerId, rigId, fishData)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "hook data not fish info")
	}

	fishData.Result = commonPB.FISH_RESULT_FR_FISH_OUT
	// 根据客户端传值 修改鱼状态
	switch fishRet {
	case commonPB.FISH_RESULT_FR_FISH_OUT:
	case commonPB.FISH_RESULT_FR_HOOKED:
		fishData.Result = fishRet
	default:
		entry.Errorf("player:%d, rigId:%d, fish status:%d error", playerId, rigId, fishRet)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "fish status error")
	}

	hookData.FishData = fishData.String()
	// 如果鱼逃跑 设置无效权重时间 下次中鱼重置初始时间
	if fishData.Result == commonPB.FISH_RESULT_FR_FISH_OUT {
		hookData.InvalidWeightTime = timex.Now().Unix()
	}

	// 更新数据
	err := daoHook.UpdatePlayerHookData(ctx, playerId, hookData)
	if err != nil {
		entry.Errorf("update player:%d, rigId:%d hook data err:%v", playerId, rigId, err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "update hook data error")
	}

	entry.Debugf("player:%d, rigId:%d, fishData:%+v", playerId, rigId, fishData)

	return fishData, nil
}

// PlayerCatchRod 玩家收杆
// 返回 饵id 鱼结果和鱼数据
func PlayerCatchRod(ctx context.Context, playerId uint64, rigId int32, hookHabit *commonPB.HookHabitParam) (*commonPB.HookBait, commonPB.FISH_RESULT, *modelHook.FishData) {
	entry := logx.NewLogEntry(ctx)

	// 查询中鱼信息
	hookData := daoHook.QueryPlayerHookData(ctx, playerId, rigId)
	if hookData == nil {
		entry.Errorf("player:%d, rigId:%d hook data not exist", playerId, rigId)
		return nil, commonPB.FISH_RESULT_FR_NOTHING, nil
	}

	if hookHabit == nil {
		entry.Errorf("player:%d, rigId:%d hook habit param is nil", playerId, rigId)

		return hookData.GetHookBait(), commonPB.FISH_RESULT_FR_NOTHING, nil
	}

	// 查看是否有鱼
	fishData := hookData.GetFishData()

	fishResult := commonPB.FISH_RESULT_FR_NOTHING
	if fishData != nil {
		fishResult = fishData.Result
	}

	hookData.FishData = ""

	// 有鱼 且中鱼状态
	if fishData.IsValid() && fishResult == commonPB.FISH_RESULT_FR_HOOKED {
		// 重置收杆时间
		hookData.CatchTime = 0
	} else {
		fishData = nil
		now := timex.Now().UnixMilli()
		// 未中鱼且鱼权重正常 保存收杆时间 下次抛竿判断是否使用上次空鱼权重
		if hookData.InvalidWeightTime <= 0 {
			hookData.CatchTime = now
		}

		stockConf := logicConf.GetPondStockConf(ctx, hookData.PondId)
		if stockConf == nil {
			hookData.EmptyWeight = config.INIT_EMPTY_FISH_WEIGHT
			entry.Errorf("stockId:%d stock conf not exist", hookData.PondId)
		} else {
			fishWg := logicWeight.CalcPondFishHabitFishWeight(ctx, hookData.PondId, hookData.BaitId, hookHabit)
			totalWeight := logicWeight.CalcFishTotalWeight(ctx, fishWg)

			newEmptyWg := logicTime.CalcNewEmptyWeight(ctx, totalWeight, hookData)
			hookData.EmptyWeight = newEmptyWg
		}
		entry.Debugf("update empty weight hookData:%+v", hookData)
	}

	daoHook.UpdatePlayerHookData(ctx, playerId, hookData)

	entry.Debugf("player:%d, rigId:%d, hookHabit:%+v, fishData:%+v, hookData:%+v", playerId, rigId, hookHabit.String(), fishData, hookData)

	return hookData.GetHookBait(), fishResult, fishData
}

// HookFishStart 开始中鱼
func HookFishStart(ctx context.Context, playerId uint64, req *hookRpc.HookStartRouteReq) (*commonPB.FishSyncControl, error) {
	entry := logx.NewLogEntry(ctx)

	rigId := req.GetRigId()
	pondId := req.GetPondId()
	hookBait := req.GetHookBait()
	hookHabit := req.GetHookHabit()
	calcType := req.GetCalcType()

	if hookHabit == nil || calcType == commonPB.HOOK_FISH_CALC_TYPE_HFCT_UNKNOWN {
		entry.Errorf("hook habit not exist")
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "habit or calc type empty")
	}

	return DealInitHookTime(ctx, playerId, pondId, rigId, hookBait, hookHabit, calcType)
}

// DealInitHookTime 处理初始化中鱼时间
func DealInitHookTime(ctx context.Context, playerId uint64, pondId int64, rigId int32, hookBait *commonPB.HookBait, hookHabit *commonPB.HookHabitParam, calcType commonPB.HOOK_FISH_CALC_TYPE) (*commonPB.FishSyncControl, error) {
	entry := logx.NewLogEntry(ctx)

	if hookHabit == nil {
		entry.Errorf("hook habit not exist")
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "habit empty")
	}

	// 查询玩家钓鱼信息
	hookData := daoHook.QueryPlayerHookData(ctx, playerId, rigId)
	// 数据异常 换场 换饵 都重置空鱼权重
	if hookData == nil || hookData.PondId != pondId || hookData.BaitId != hookBait.GetBaitId() {
		hookData = &modelHook.HookData{PlayerId: playerId, RigId: rigId, PondId: pondId, BaitId: hookBait.GetBaitId()}
		entry.Debugf("first time hook data:%+v", hookData)
	}

	// 重新赋值饵id
	if hookBait != nil {
		hookData.BaitId = hookBait.GetBaitId()
		hookData.HookId = hookBait.GetHookId()
	}

	// 对比上次抽竿时间 判断是否使用上次空鱼权重(秒)
	nowTime := timex.Now().UnixMilli()

	// 中鱼静态配置
	hookConstConf := cmodel.GetHookConst(consul_config.WithGrpcCtx(ctx))
	if hookConstConf == nil {
		entry.Errorf("hook const conf not exist")
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "hook const conf not exist")
	}

	controlInfo := &commonPB.FishSyncControl{
		StopTime: int64(1800 * 1000), // 默认
	}

	var emptyWg int32 = 0

	// 计算实鱼权重
	fishWeight := logicWeight.CalcPondFishHabitFishWeight(ctx, pondId, hookBait.GetBaitId(), hookHabit)
	totalWg := logicWeight.CalcFishTotalWeight(ctx, fishWeight)

	// 鱼的总权重为0时 标记时间为当前时间 直接返回默认中鱼请求间隔
	if totalWg <= 0 {
		controlInfo.IntervalTime = hookConstConf.ReqFishInterval
		hookData.InvalidWeightTime = timex.Now().Unix()
		daoHook.UpdatePlayerHookData(ctx, playerId, hookData)
		entry.Warnf("fish weight is zero, pondId:%d hook data:%+v, hookHabit:%+v", pondId, hookData, hookHabit.String())
		return controlInfo, nil
	}

	// 1.判断是否使用上次的空鱼权重(开始计算中鱼)
	useLastNewTime := logicWeight.CheckUseLastEmptyWeight(ctx, playerId, calcType, hookData, totalWg)
	if useLastNewTime > 0 {
		emptyWg = hookData.EmptyWeight
		hookData.ReloadHookData(emptyWg, useLastNewTime)
		daoHook.UpdatePlayerHookData(ctx, playerId, hookData)
		// 通知客户端中鱼时间
		controlInfo.IntervalTime = useLastNewTime

		return controlInfo, nil
	}

	// 2.计算空鱼 初始时间 实鱼权重
	ratio := logicTime.CalcHookUndulateRatio(ctx, pondId, totalWg, emptyWg)
	initTime, emptyWg := logicTime.CalcInitHookFishTime(ratio, totalWg)

	// 下发中鱼请求时间
	reqTimeMsec := initTime
	if initTime > hookConstConf.ReqFishInterval {
		reqTimeMsec = hookConstConf.ReqFishInterval
	}

	if reqTimeMsec <= 0 {
		entry.Errorf("hook data:%+v, empty weight:%d, init time:%d, now time:%d", hookData, emptyWg, initTime, nowTime)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, "init time is zero")
	}

	// 更新数据
	hookData.ReloadHookData(emptyWg, initTime)
	daoHook.UpdatePlayerHookData(ctx, playerId, hookData)

	// 赋值结果
	controlInfo.IntervalTime = reqTimeMsec
	controlInfo.StopTime = initTime

	entry.Debugf("rigId:%d, hookBait:%+v, hookHabit:%+v, hookData:%+v, controlInfo:%+v", rigId, hookBait, hookHabit, hookData, controlInfo)

	return controlInfo, nil
}
