package logicWeight

import (
	"context"
	"math"

	logicConf "hook2srv/internal/logic/logic_conf"
	logicHabit "hook2srv/internal/logic/logic_habit"
	"hook2srv/internal/repo"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/algorithm/lottery"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

// GetPondFishInitWeight 获取鱼基础权重
func GetPondFishInitWeight(ctx context.Context, pondId int64) map[int64]int32 {
	entry := logx.NewLogEntry(ctx)

	// 查询钓场信息
	pondConf := cmodel.GetFishPondList(pondId, consul_config.WithGrpcCtx(ctx))
	if pondConf == nil { 
		entry.Errorf("GetFishPondList pondId:%d config not exist", pondId)
		return nil
	}

	// 投放属性映射配置
	stockReleaseConf := cmodel.GetAllStockRelease(consul_config.WithGrpcCtx(ctx))
	if stockReleaseConf == nil {
		entry.Errorf("pondId:%d StockRelease conf not exist", pondId)
		return nil
	}

	// 投鱼配置信息
	fishReleaseConf := cmodel.GetAllFishRelease(consul_config.WithGrpcCtx(ctx))
	if fishReleaseConf == nil {
		entry.Errorf("pondId:%d FishRelease conf not exist", pondId)
		return nil
	}

	fishWeight := make(map[int64]int32)
	for _, fishRelease := range stockReleaseConf {
		if fishRelease.StockId == pondConf.FishStockId {
			releaseInfo, ok := fishReleaseConf[fishRelease.ReleaseId]
			if !ok {
				entry.Errorf("stockId:%d FishReleaseId:%d config not exist", pondConf.FishStockId, fishRelease.ReleaseId)
				continue
			}

			fishWeight[fishRelease.FishId] = releaseInfo.ProbWeightIdeal
		}
	}

	return fishWeight
}

// CalcFishTotalWeight计算实鱼总权重
func CalcFishTotalWeight(ctx context.Context, fishWeight map[int64]int32) int32 {
	entry := logx.NewLogEntry(ctx)

	if len(fishWeight) <= 0 {
		entry.Errorf("fish weight is nil")
		return 0
	}

	var totalWeight int32

	for _, weight := range fishWeight {
		totalWeight += weight
	}

	return totalWeight
}

// GetRandFishForWeight 根据鱼的权重随机一条鱼
func GetRandFishForWeight(ctx context.Context, fishWeight map[int64]int32) int64 {
	entry := logx.NewLogEntry(ctx)

	if len(fishWeight) <= 0 {
		entry.Errorf("fish weight is nil")
		return 0
	}

	items := make([]lottery.Item, 0, len(fishWeight))
	// 根据权重随机一条鱼
	for fishId, weight := range fishWeight {
		if weight <= 0 {
			continue
		}

		items = append(items, lottery.Item{Value: transform.Int642Str(fishId), Weight: int(weight)})
	}

	if len(items) <= 0 {
		entry.Warnf("fishWeight:%v, get fish empty", len(fishWeight))
		return 0
	}

	// 只抽取一个样本
	chooseOne := lottery.WeightedReservoirSampling(items, 1)
	if len(chooseOne) <= 0 {
		entry.Warnf("fishWeight:%v, get fish empty", fishWeight)
		return 0
	}

	// 随机一条
	strFish := chooseOne[0].Value
	fishId := transform.Str2Int64(strFish)

	entry.Debugf("fishWeight:%+v, choose fish:%d", fishWeight, fishId)

	return fishId
}

// CalcPondFishHabitFishWeight 计算钓场中鱼习性权重
func CalcPondFishHabitFishWeight(ctx context.Context, pondId int64, baitId int64, hookHabit *commonPB.HookHabitParam) map[int64]int32 {
	entry := logx.NewLogEntry(ctx)
	if hookHabit == nil {
		entry.Errorf("pondId:%d, hookHabit is nil", pondId)
		return nil
	}

	waterTemp := hookHabit.GetWaterTemp()
	structList := hookHabit.GetStructureList()
	layerList := hookHabit.GetLayerList()
	poseInfo := hookHabit.GetBaitPoseInfo()
	logLight := hookHabit.GetLogLight()

	if len(structList) <= 0 || len(layerList) <= 0 {
		entry.Errorf("pondId:%d, structList or layerList is nil", pondId)
		return nil
	}

	// 1.钓场配置
	pondConf := logicConf.GetPondStockConf(ctx, pondId)
	if pondConf == nil {
		entry.Errorf("GetPondStockConf failed, pondId: %d", pondId)
		return nil
	}

	// 2.获取鱼的投放信息
	fishReleaseMap := logicConf.GetPondFishReleaseInfo(ctx, pondConf.Id)
	if len(fishReleaseMap) <= 0 {
		entry.Errorf("GetPondFishReleaseInfo empty, pondId: %d", pondId)
		return nil
	}

	envFishMap := logicConf.GetPondEvnFishMap(ctx, pondConf.Id)

	// 3.根据鱼饵筛选鱼
	baitCoeff := logicHabit.FilterPondFishForBait(ctx, baitId, envFishMap)
	
	// 重新组合 删除系数无效的鱼 fishEnvId:fishId
	for fishEvnId, fishId := range envFishMap {
		if baitCoeff[fishId] <= 0 { 
			delete(envFishMap, fishEvnId)
		}
	}

	// 查询天气
	weatherInfo := repo.RpcGetWeather(ctx, pondId)
	
	// 4.根据结构体计算鱼的亲和系数
	structCoeff := logicHabit.CalcFishStructCoeff(ctx, structList, envFishMap)

	// 5.根据水温计算鱼的亲和系数
	waterCoeff := logicHabit.CalcFishWaterTempCoeff(ctx, waterTemp, envFishMap)

	// 6.根据水层计算鱼的亲和系数
	layerCoeff := logicHabit.CalcFishWaterLayerCoeff(ctx, layerList, envFishMap)

	// 7.计算鱼饵姿态系数
	baitTypePoseCoeff := logicHabit.CalcBaitPoseCoeff(ctx, baitId, poseInfo, envFishMap)

	// 8.计算饵长度亲和系数
	baitLenCoeff := logicHabit.CalcBaitLengthCoeff(ctx, baitId, fishReleaseMap, envFishMap)

	// 9.计算光照系数 TODO 
	// lightCoeff := logicHabit.CalcFishLightCoeff(ctx, logLight, fishList)

	// 10.计算气压系数
	pressureCoeff := logicHabit.CalcFishPressureCoeff(ctx, weatherInfo.GetData().GetPressureInfluence(), envFishMap)

	// 11. 计算时段系数
	periodCoeff := logicHabit.CalcFishTimePeriodCoeff(ctx, weatherInfo.GetTime(), envFishMap)

	fishWeight := make(map[int64]int32, len(envFishMap))

	// 12.组合所有系数 即所有的系数相乘*鱼基础权重
	for _, fishId := range envFishMap {
		initWg := fishReleaseMap[fishId].ProbWeightIdeal

		// 饵长度亲和系数 没有默认为1
		lenCoeff, ok := baitLenCoeff[fishId]
		if !ok {
			lenCoeff = 1.0
		}

		// 1.饵亲和系数 = 饵类型亲和系数 * 姿态亲和系数 * 长度亲和系数
		baitTypeCoeff := baitCoeff[fishId] * baitTypePoseCoeff[fishId] * lenCoeff

		// 2.适配系数阈值校验 max(饵类型亲和系数, fishRelease.MinAdaptCoeff)
		curBaitCoeff := math.Max(baitTypeCoeff, fishReleaseMap[fishId].MinAdaptCoeff)

		// 3.天气系数 = 光照亲和系数 * 气压亲和系数
		weatherCoeff := pressureCoeff[fishId] //* lightCoeff[fishId]

		// 4.环境系数最低阈值 max(环境适配系数, fishRelease.MinEnvCoeff)
		envCoeff := math.Max(structCoeff[fishId] * waterCoeff[fishId] * layerCoeff[fishId] * weatherCoeff * periodCoeff[fishId], fishReleaseMap[fishId].MinEnvCoeff)

		habitWg := int32(math.Round(float64(initWg) * curBaitCoeff * envCoeff))

		fishWeight[fishId] = habitWg
	}

	entry.Debugf("pondId:%d, baitId:%d, waterTemp:%d, structList:%+v, logLight:%f, layerList:%+v fishWeight:%v", pondId, baitId, waterTemp, structList, logLight, layerList, fishWeight)

	return fishWeight
}
