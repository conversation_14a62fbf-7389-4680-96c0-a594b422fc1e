package logicWeight

import (
	"context"
	"fmt"
	test_init "hook2srv/internal/test"
	"testing"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func TestCalcPondFishHabitFishWeight(t *testing.T) {
	test_init.InitRedisConsul()
	ctx := context.Background()

	pondId := int64(301020001)
	baitId := int64(309301001)
	hookHabit := &commonPB.HookHabitParam{
		WaterTemp:     25,
		LayerList:     []commonPB.MAP_WATER_LAYER_TYPE{commonPB.MAP_WATER_LAYER_TYPE_MWLT_MIDDLE, commonPB.MAP_WATER_LAYER_TYPE_MWLT_BOTTOM},
		StructureList: []commonPB.UNDER_WATER_STRUCTURE{commonPB.UNDER_WATER_STRUCTURE_UWS_STONE, commonPB.UNDER_WATER_STRUCTURE_UWS_DRIFTWOOD},
		BaitPoseInfo: &commonPB.HookBaitTypePose{
			PoseType: commonPB.FISHING_BAIT_TRICK_TYPE_FBTT_SHAKING,
			Score:    80,
		},
	}

	// 测试接口消耗时间 毫秒

	tStart := time.Now().UnixMicro()

	CalcPondFishHabitFishWeight(ctx, pondId, baitId, hookHabit)
	tEnd := time.Now().UnixMicro()
	fmt.Printf("CalcPondFishHabitFishWeight 耗时: %v ms", (tEnd-tStart)/1000)
}
