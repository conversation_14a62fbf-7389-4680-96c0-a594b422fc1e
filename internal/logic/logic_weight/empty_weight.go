package logicWeight

import (
	"context"
	"time"

	modelHook "hook2srv/internal/model/model_hook"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

// CheckUseLastEmptyWeight 检查是否使用上次空鱼权重 返回下次中鱼请求时间
func CheckUseLastEmptyWeight(ctx context.Context, playerId uint64, calcType commonPB.HOOK_FISH_CALC_TYPE, hookData *modelHook.HookData, totalWg int32) int64 {
	entry := logx.NewLogEntry(ctx)

	if hookData == nil {
		entry.Errorf("hook habit param is nil")
		return 0
	}

	// 中鱼静态配置
	hookConstConf := cmodel.GetHookConst(consul_config.WithGrpcCtx(ctx))
	if hookConstConf == nil {
		entry.Errorf("hook const conf not exist")
		return 0
	}

	nowTime := time.Now().UnixMilli()

	// 数据无效 或者时间过了
	if  hookConstConf.EmptyFishWgReset <= 0 || hookData.CatchTime <= 0 || hookData.CatchTime+int64(hookConstConf.EmptyFishWgReset) < nowTime && hookData.EmptyWeight <= 0 {
		return 0
	}

	var newTime int64 = 0

	// 不是重置中鱼时间且无无效权重
	if calcType != commonPB.HOOK_FISH_CALC_TYPE_HFCT_RESET && hookData.InvalidWeightTime <= 0 {
		// 使用上次的空鱼权重直接计算时间 
		newTime = int64(1000 *  hookData.EmptyWeight / totalWg)
		if newTime <= 0 {
			newTime = 1
		}

		if newTime > hookConstConf.ReqFishInterval {
			newTime = hookConstConf.ReqFishInterval
		}
	}


	entry.Debugf("use last empty weight, hook data:%+v, newTime:%d, totalWg:%d", hookData, newTime, totalWg)

	return newTime
}