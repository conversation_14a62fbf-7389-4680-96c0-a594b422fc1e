package logicTime

import (
	"hook2srv/config"
	modelHook "hook2srv/internal/model/model_hook"

	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
)

// CalcInvalidWeightNextTime 计算无效权重下一次请求时间
func CalcInvalidWeightNextTime(hookData *modelHook.HookData, defaultTime int64) int64 {
	if hookData.InvalidWeightTime <= 0 {
		return 0
	}

	nowTime := timex.Now().Unix()

	// 超过指定时间 直接返回固定的请求时间
	if nowTime - hookData.InvalidWeightTime > config.FISH_WEIGHT_EMPTY_MAX_TIME {
		return config.FISH_WEIGHT_EMPTY_INTERVAL
	} else {
		return defaultTime
	}
}

// CalcInitHookFishTimeWeight 计算初始中鱼时间 返回初始时间和空鱼权重
func CalcInitHookFishTime(ratio float64, totalWg int32) (int64, int32) {
	// 初始时间和空鱼权重
	initTime := int64(ratio * 1000)
	emptyWg := int32(ratio * float64(totalWg))

	return initTime, emptyWg
}

// CheckReqTimeLimit 请求时间限制逻辑 防刷
func CheckReqTimeLimit(hookData *modelHook.HookData, intervalMsec int64) bool {
	if hookData == nil {
		return false
	}

	if intervalMsec <= 0 {
		return false
	}

	nowTime := timex.Now().UnixMilli()
	reqInterval := nowTime - hookData.LastTime

	if hookData.LeftMsec <= intervalMsec {
		// 小于间隔时间限制 且 请求间隔小于剩余时间 - 延迟时间 
		if reqInterval < hookData.LeftMsec - config.HOOK_FISH_REQ_TIME_DELAY {
			return true
		}
	} else {
		// 大于间隔时间限制 且 请求间隔小于间隔时间 - 延迟时间
		if reqInterval < intervalMsec - config.HOOK_FISH_REQ_TIME_DELAY {
			return true
		}
	}

	return false
}