package logicTime

import (
	"context"
	"hook2srv/config"
	"math"

	logicConf "hook2srv/internal/logic/logic_conf"
	modelHook "hook2srv/internal/model/model_hook"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
)

// CalcHookUndulateRatio 计算中鱼波动系数(浮点数 后面计算时间的时候乘以1000 取整)
// 计算公式:
// r = fishWg / emptyWg //实鱼权重和空鱼权重比
// t = -timeExpect / r * ln(1- random(1-e^-maxTmRatio))
func CalcHookUndulateRatio(ctx context.Context, pondId int64, fishWg, emptyWg int32) float64 {
	entry := logx.NewLogEntry(ctx)

	stockConf := logicConf.GetPondStockConf(ctx, pondId)
	if stockConf == nil {
		entry.Errorf("pondId:%d stock config error", pondId)
		return 0
	}

	if emptyWg <= 0 {
		emptyWg = stockConf.NoneFishWeight
	}

	u := random.Float64N(1-math.Exp(-stockConf.MaxTimeMultiplier))

	r := float64(fishWg) / float64(emptyWg)
	t := -(stockConf.TimeExpectFactor / r) * math.Log(1-u)

	entry.Infof("fishWg:%d, emptyWg:%d, maxTmRatio:%f, timeExpect:%f, r:%f, u:%f, t:%f", fishWg, emptyWg, stockConf.MaxTimeMultiplier, stockConf.TimeExpectFactor, r, u, t)

	return t
}

// CalcNewEmptyWeight 计算新的空鱼权重
func CalcNewEmptyWeight(ctx context.Context, fishWg int32, hookData *modelHook.HookData) int32 {
	entry := logx.NewLogEntry(ctx)

	if fishWg == 0 || hookData == nil {
		entry.Warnf("fishWg:%+v or hookData:%+v is invalid use init empty weight:%d", fishWg, hookData, config.INIT_EMPTY_FISH_WEIGHT)
		return config.INIT_EMPTY_FISH_WEIGHT
	}

	nowTime := timex.Now().UnixMilli()

	// 间隔时间
	if hookData.LastTime == 0 {
		hookData.LastTime = nowTime
	}

	timeInterval := nowTime - hookData.LastTime

	newEmptyWg := hookData.EmptyWeight - int32(timeInterval*int64(fishWg) / 1000)

	entry.Debugf("fishWg:%d, hookData:%+v, nowTime:%d, timeInterval:%d, newEmptyWg:%d", fishWg, hookData, nowTime, timeInterval, newEmptyWg)

	return newEmptyWg
}