package logicFish

import (
	"context"
	"fmt"
	daoHook "hook2srv/internal/dao/dao_hook"
	logicWeight "hook2srv/internal/logic/logic_weight"
	modelHook "hook2srv/internal/model/model_hook"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
)

// CheckPlayerFixFish 检查玩家是否固定中鱼
func CheckPlayerFixFish(ctx context.Context, playerId uint64, pondId int64) (int64, error) {
	entry := logx.NewLogEntry(ctx)

	// 查询玩家是否固定中鱼
	fixFish, err := daoHook.QueryPlayerFixHookFish(ctx, playerId)
	if err != nil || fixFish == nil {
		return 0, err
	}

	fishWeight := logicWeight.GetPondFishInitWeight(ctx, pondId)

	// 随机中鱼
	if fixFish.IsRand {
		if len(fishWeight) <= 0 {
			entry.Warnf("check player:%d fix hook pond:%d, get fish list error:%+v", playerId, pondId, err)
			return 0, err
		}

		// 从fishMap中随机一个
		keys := make([]int64, len(fishWeight))
		i := 0
		for k := range fishWeight {
			keys[i] = k
			i++
		}
		randIndex := random.IntN(len(keys))

		fishBase := keys[randIndex]
		fixFish.FishId = fishBase
	} else {
		// 校验钓场中是否有这条鱼
		fishWg := fishWeight[fixFish.FishId]
		if fishWg <= 0 {
			entry.Warnf("check player:%d fix hook pond:%d, fishId:%d not in pond", playerId, pondId, fixFish.FishId)
			return 0, fmt.Errorf("fishId:%d not in pond:%d", fixFish.FishId, pondId)
		}
	} 

	entry.Debugf("player:%d in pond:%d fix hook fish:%d, is rand:%t", playerId, pondId, fixFish.FishId, fixFish.IsRand)

	return fixFish.FishId, nil
}

// GetFixFishInfo 获取玩家固定中鱼信息
func GetFixFishInfo(ctx context.Context, playerId uint64, pondId int64, hookData *modelHook.HookData) *commonPB.FishInfo{
	
	if hookData == nil {
		return nil
	}

	entry := logx.NewLogEntry(ctx)

	fishId, err := CheckPlayerFixFish(ctx, playerId, pondId)
	if err != nil && fishId <= 0 {
		return nil
	}
	
	// 记录玩家中鱼信息 
	hookData.InsertFishData(ctx, fishId, 0)

	entry.Infof("pondId:%d, in check fix fish id:%d", pondId, fishId)
 
	return hookData.GetFishData().ToProto(ctx)
}