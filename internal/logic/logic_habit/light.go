package logicHabit

import (
	"context"
	"math"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

// 光照系数

// CalcFishLightCoeff 计算光照亲和系数
func CalcFishLightCoeff(ctx context.Context, logLight float64, fishList []int64) map[int64]float64 {
	entry := logx.NewLogEntry(ctx)

	if len(fishList) == 0 {
		entry.Error("fishList is empty")
		return nil
	}

	// 查询所有鱼环境系数配置
	allFishEnvAffConf := cmodel.GetAllFishEnvAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allFishEnvAffConf) <= 0 {
		entry.Errorf("GetAllFishEnvAffinity config is empty")
		return nil
	}

	// 查询所有光照系数配置
	allLightAffConf := cmodel.GetAllLightAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allLightAffConf) <= 0 {
		entry.Errorf("GetAllLightAffinity config is empty")
		return nil
	}

	// 计算光照亲和系数
	fishLightCoeff := make(map[int64]float64, len(fishList))
	
	for _, fishId := range fishList {
		fishEnvAff, envOk := allFishEnvAffConf[fishId]
		if !envOk {
			continue
		}
		
		lightAff, structOk := allLightAffConf[fishEnvAff.LightId]
		if !structOk {
			continue
		}
		
		fishLightCoeff[fishId] = CalcFishLight(logLight, lightAff)
	}

	entry.Debugf("logLight:%f fishLightCoeff: %+v", logLight, fishLightCoeff)

	return fishLightCoeff
}

// CalcFishLight 计算光照亲和系数  
// exponent = -((logLight - lightConf.PreferLight) ** 2) / (2 * (lightConf.LightTolerance ** 2))    
// lightCoeff = exp(exponent) 
// if lightCoeff < lightConf.LightThreshold:
//         lightCoeff = 0.0
func CalcFishLight(logLight float64, lightConf *cmodel.LightAffinity) float64 {
	if lightConf == nil {
		return 0.0
	}

	exponent := -((logLight - lightConf.PreferLight) * (logLight - lightConf.PreferLight)) / (2 * (lightConf.LightTolerance * lightConf.LightTolerance))

	lightCoeff := math.Exp(exponent)
	if lightCoeff < lightConf.LightThreshold {
		lightCoeff = 0.0
	}

	return lightCoeff
}
  