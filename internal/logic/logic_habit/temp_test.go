package logicHabit

import (
	"context"
	test_init "hook2srv/internal/test"
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
)

func TestCalcTempCoeff(t *testing.T) {

	testA := []int32{1, 2, 3,4,5}
	testB := make(map[int32]int32, len(testA))

	for _, v := range testA {
		if v == 1 {
			continue
		}
		testB[v] = v
	}
	t.Logf("testB: %+v", testB)

 
	test_init.InitRedisConsul()
	ctx := context.Background()
	tempAffConf := cmodel.GetTempAffinity(1)
	affGlobal := cmodel.GetEnvAffinityConst()
	coeff := CalcTempCoeff(ctx, 230, tempAffConf, affGlobal)
	t.Logf("coeff: %+v", coeff)
}