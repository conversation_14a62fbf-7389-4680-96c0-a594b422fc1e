package logicHabit

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// 结构体系数

// CalcFishStructCoeff 计算鱼水下结构体习性系数值
func CalcFishStructCoeff(ctx context.Context, typeList []commonPB.UNDER_WATER_STRUCTURE, envFishMap map[int64]int64) map[int64]float64 {
	entry := logx.NewLogEntry(ctx)

	if len(typeList) == 0 || len(envFishMap) == 0 {
		entry.Errorf("typeList:%+v or envFishMap:%+v is empty", typeList, envFishMap)
		return nil
	}

	// 查询所有鱼环境系数配置
	allFishEnvAffConf := cmodel.GetAllFishEnvAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allFishEnvAffConf) <= 0 {
		entry.Errorf("GetAllFishEnvAffinity config is empty")
		return nil
	}

	// 查询所有结构体系数配置
	allStructConf := cmodel.GetAllStructAffinity(consul_config.WithGrpcCtx(ctx))
	if allStructConf == nil {
		entry.Errorf("GetAllStructAffinity config is nil")
		return nil
	}

	coeffMap := make(map[int64]float64, len(envFishMap))

	// 根据配置过滤取最大值
	for fishEnvId, fishId := range envFishMap {
		fishEnvAff, envOk := allFishEnvAffConf[fishEnvId]
		if !envOk {
			continue
		}

		structCoeffConf, structOk := allStructConf[fishEnvAff.StructId]
		if !structOk {
			continue
		}

		coeffMap[fishId] = CalcMaxCoeffForStructType(typeList, structCoeffConf.List)
	}

	entry.Debugf("typeList:%+v, fishList:%+v, coeffMap:%+v", typeList, envFishMap, coeffMap)

	return coeffMap
}

// CalcMaxCoeffForStructType 根据结构体类型计算最大系数
func CalcMaxCoeffForStructType(typeList []commonPB.UNDER_WATER_STRUCTURE, affList []cmodel.StructAffinityList) float64 {
	// 1. 建立类型映射快速匹配
	typeIDSet := make(map[int32]struct{}, len(typeList))
	for _, t := range typeList {
		typeIDSet[int32(t)] = struct{}{}
	}

	// 2. 单次遍历求最大值 [3]()
	var maxCoeff float64
	for _, aff := range affList {
		if _, exists := typeIDSet[aff.StructType]; exists {
			if aff.Coeff > maxCoeff {
				maxCoeff = aff.Coeff
			}
		}
	}

	return maxCoeff
}
