package logicHabit

import (
	"context"
	"math"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// 气压系数

// CalcFishPressureCoeff 计算鱼的气压系数
func CalcFishPressureCoeff(ctx context.Context, pressure int64, envFishMap map[int64]int64) map[int64]float64 {
	entry := logx.NewLogEntry(ctx)
	if len(envFishMap) == 0 || pressure <= 0 {
		entry.Errorf("calc fish pressure coeff error, envFishMap empty or pressure is zero, envFishMap: %v, pressure: %d", envFishMap, pressure)
		return nil
	}
	
	// 查询所有鱼环境系数配置
	allFishEnvAffConf := cmodel.GetAllFishEnvAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allFishEnvAffConf) <= 0 {
		entry.Erro<PERSON>("GetAllFishEnvAffinity config is empty")
		return nil
	}

	// 气压激活系数 = 压力 / 10000
	pressureActCoeff := float64(pressure) / 10000

	// 计算气压系数
	coeffMap := make(map[int64]float64, len(envFishMap))
	for fishEnvId, fishId := range envFishMap {
		fishEnvAff, envOk := allFishEnvAffConf[fishEnvId]
		if !envOk {
			continue
		}
		
		// 计算气压系数 = math.Pow(气压激活系数，气压敏感度)
		coeffMap[fishId] = math.Pow(pressureActCoeff, fishEnvAff.PressureSensitivity)
	}

	entry.Debugf("calc fish pressure:%d, envFishMap:%+v, coeff:%+v", pressure, envFishMap, coeffMap)

	return coeffMap
}