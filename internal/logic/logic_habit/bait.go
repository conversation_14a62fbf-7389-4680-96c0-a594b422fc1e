package logicHabit

import (
	"context"
	logicConf "hook2srv/internal/logic/logic_conf"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// 鱼饵系数

// FilterPondFishForBait 根据鱼饵过滤钓场中的鱼
func FilterPondFishForBait(ctx context.Context, baitId int64, envFishMap map[int64]int64) map[int64]float64 {
	entry := logx.NewLogEntry(ctx)

	if baitId <= 0 {
		entry.Errorf("baitId is invalid, baitId: %d", baitId)
		return nil
	}

	// 1.查询鱼饵信息
	baitInfo := cmodel.GetItem(baitId, consul_config.WithGrpcCtx(ctx))
	if baitInfo == nil {
		entry.Errorf("GetItem failed, baitId: %d", baitId)
		return nil
	} 

	// 2.根据鱼饵的配置过滤鱼
	baitCoeffConf := logicConf.GetFishBaitCoeffList(ctx, baitId, envFishMap)
	baitTypeCoeff := logicConf.GetFishBaitTypeCoeffList(ctx, baitInfo.SubType, envFishMap)

	// 以饵类型为准 没有配置饵类型的直接过滤钓 没有配置饵的默认系数取1
	for fishId, typeCoeff := range baitTypeCoeff {
		if coeff, ok := baitCoeffConf[fishId]; ok {
			baitTypeCoeff[fishId] = typeCoeff * coeff
		}
	}


	entry.Debugf("baitId:%d, filter bait coeff: %+v", baitId, baitTypeCoeff)

	return baitTypeCoeff
}