package logicHabit

import (
	"context"
	"math"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// 饵长度中鱼感知系数

// CalcBaitLengthCoeff 计算鱼饵长度适配系数
func CalcBaitLengthCoeff(ctx context.Context, baitId int64, fishReleaseMap map[int64]*cmodel.FishRelease, envFishMap map[int64]int64) map[int64]float64 {
	entry := logx.NewLogEntry(ctx)

	if len(fishReleaseMap) == 0 || len(envFishMap) == 0 {
		entry.Errorf("fishReleaseMap:%+v or envFishMap:%+v is empty", fishReleaseMap, envFishMap)
		return nil
	}

	// 鱼饵信息
	baitInfo := cmodel.GetItem(baitId, consul_config.WithGrpcCtx(ctx))
	if baitInfo == nil {
		entry.Errorf("GetItem failed, baitId: %d", baitId)
		return nil
	}

	// 这里只用处理拟饵 真饵不用处理
	if baitInfo.ItemType != int32(commonPB.ITEM_TYPE_IT_TACKLE_LURES) {
		entry.Warnf("bait type is not lures, baitId: %d", baitId)
		return nil
	}

	baitDetailInfo := cmodel.GetLures(baitId, consul_config.WithGrpcCtx(ctx))
	if baitDetailInfo == nil {
		entry.Errorf("GetLures failed, baitId: %d", baitId)
		return nil
	}

	// 所有鱼环境系数配置
	allFishEnvAffConf := cmodel.GetAllFishEnvAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allFishEnvAffConf) <= 0 {
		entry.Errorf("GetAllFishEnvAffinity config is empty")
		return nil
	}

	// 饵感知长度 = 饵长度 * 饵感知系数长度
	baitPerceptionLen := float64(baitDetailInfo.Length) * float64(baitDetailInfo.LengthFactor)

	coeffMap := make(map[int64]float64, len(envFishMap))

	for fishEnvId, fishId := range envFishMap {
		fishEnvConf, envOk := allFishEnvAffConf[fishEnvId]
		if !envOk {
			continue
		}

		fishRelease, releaseOk := fishReleaseMap[fishId]
		if !releaseOk {
			continue
		}

		coeffMap[fishId] = CalcFishBaitLengthCoeff(ctx, baitPerceptionLen, fishEnvConf, fishRelease)
	}

	entry.Debugf("baitId:%d, envFishMap:%+v, coeffMap:%+v", baitId, envFishMap, coeffMap)

	return coeffMap
}

/*
饵感知长度 = 饵长度 * 饵感知长度系数
max 鱼种 喜好长度 = 钓场中 鱼种max长度 * 鱼种 max适应饵长度比
min 鱼种 喜好长度 = 钓场中 鱼种min长度 * 鱼种 min适应饵长度比
Max鱼种接受饵长 = 钓场中 鱼种max长度 * 鱼种 max接受长度比例
If 饵感知长度 > Max鱼种接受饵长：
饵长度适配度 = 0
Return 饵长度适配度
if 饵感知长度 > max 鱼种 喜欢 饵感知长度:
超出比例 = （饵感知长度 - 鱼种最大喜好长度）/ 鱼种最大喜好长度
饵长度适配度 = exp（ − 偏长饵衰减系数 * 超出比例）
Return 饵长度适配度
Elif 饵感知长度 < min 鱼种 喜好长度：
超出比例 = （鱼种最小喜好长度 - 饵感知长度） / 鱼种最小喜好长度 
饵长度适配度 = exp（− 偏短饵衰减系数 × 超出比例） 
Return 饵长度适配度
else：
饵长度适配度 = 1
Return 饵长度适配度
*/

// CalcFishBaitLengthCoeff 计算鱼对饵长度适配系数
func CalcFishBaitLengthCoeff(ctx context.Context, baitPerceptionLen float64, envConf *cmodel.FishEnvAffinity, releaseConf *cmodel.FishRelease) float64 {
	entry := logx.NewLogEntry(ctx)

	coeff := 1.0

	if envConf == nil || releaseConf == nil {
		entry.Warnf("envConf:%+v or releaseConf:%+v is empty", envConf, releaseConf)
		return coeff
	}
	
	// max喜好长度 = 鱼种max长度 * max适应饵长度比
	maxLikeLen := float64(releaseConf.LengthMax) * envConf.MaxAdaptLureRatio

	// min喜好长度 = 鱼种min长度 * min适应饵长度比
	minLikeLEn := float64(releaseConf.LengthMin) * envConf.MinAdaptLureRatio

	// max鱼种接受饵长 = 鱼种max长度 * max接受长度比例
	maxAcceptLen := float64(releaseConf.LengthMax) * envConf.MaxAcceptLengthRatio

	if baitPerceptionLen > maxAcceptLen {
		coeff =  0.0
	} else if baitPerceptionLen > maxLikeLen {
		diff := (baitPerceptionLen - maxLikeLen) / maxLikeLen
		coeff = math.Exp(-envConf.OverLengthDecayCoeff * diff)
	} else if baitPerceptionLen < minLikeLEn {
		diff := (minLikeLEn - baitPerceptionLen) / minLikeLEn
		coeff = math.Exp(-envConf.UnderLengthDecayCoeff * diff)
	} else {
		coeff = 1.0
	}

	return coeff
}