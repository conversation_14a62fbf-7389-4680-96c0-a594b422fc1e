package logicHabit

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// 鱼饵姿态(玩家手法)波动系数

// CalcBaitPoseCoeff 鱼饵姿态波动系数
func CalcBaitPoseCoeff(ctx context.Context, baitId int64, poseInfo *commonPB.HookBaitTypePose, envFishMap map[int64]int64) map[int64]float64 {
	entry := logx.NewLogEntry(ctx)

	if baitId <= 0 || poseInfo == nil || len(envFishMap) == 0 {
		entry.Errorf("baitId:%d or poseInfo:%+v or envFishMap:%+v is empty", baitId, poseInfo, envFishMap)
		return nil
	}

	// 鱼饵信息
	baitInfo := cmodel.GetItem(baitId, consul_config.WithGrpcCtx(ctx))
	if baitInfo == nil {
		entry.Errorf("GetItem failed, baitId: %d", baitId)
		return nil
	}

	// 鱼饵类型系数配置
	allBaitTypeAffConf := cmodel.GetAllBaitTypeAffinity(consul_config.WithGrpcCtx(ctx))
	if allBaitTypeAffConf == nil {
		entry.Errorf("GetFishBaitTypeCoeffList failed, baitId:%d, baitSubType: %d", baitId, baitInfo.SubType)
		return nil
	}

	// 所有鱼环境系数配置
	allFishEnvAffConf := cmodel.GetAllFishEnvAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allFishEnvAffConf) <= 0 {
		entry.Errorf("GetAllFishEnvAffinity config is empty")
		return nil
	}

	// 鱼饵姿态系数配置
	baitTypePoseCoeffConf := cmodel.GetAllPoseAffinity(consul_config.WithGrpcCtx(ctx))
	if len(baitTypePoseCoeffConf) <= 0 {
		entry.Errorf("GetAllPoseAffinity config is empty")
		return nil
	}

	coeffMap := make(map[int64]float64, len(envFishMap))

	for fishEvnId, fishId := range envFishMap {
		fishEnvAffConf, envOk := allFishEnvAffConf[fishEvnId]
		if !envOk {
			continue
		}

		for _, baitTypeAff := range allBaitTypeAffConf {
			if fishEnvAffConf.BaitTypeCoeffGroup == baitTypeAff.BaitTypeCoeffGroup && baitTypeAff.BaitSubType == baitInfo.SubType {
				
				coeff := CalcFishBaitPoseCoeff(ctx, poseInfo.GetPoseType(), poseInfo.GetScore(), baitTypeAff.PoseGroup, baitTypePoseCoeffConf)
				
				coeffMap[fishId] = coeff
				break
			}
		}
	}

	entry.Debugf("baitId:%d, envFishMap:%+v, poseInfo:%+v coeffMap:%+v", baitId, envFishMap, poseInfo, coeffMap)

	return coeffMap
}

// CalcBaitPoseCoeffForScore 根据分数计算鱼饵姿态系数
// scoreRatio = score / 100
// coeff = a * scoreRatio^2 + b * scoreRatio + c
func CalcFishBaitPoseCoeff(ctx context.Context, poseType commonPB.FISHING_BAIT_TRICK_TYPE, score int32, poseGroup int64, allPoseConf map[int64]*cmodel.PoseAffinity) float64 {
	entry := logx.NewLogEntry(ctx)

	if len(allPoseConf) <= 0 {
		entry.Errorf("poseConf is empty")
		return 0
	}

	var poseCoeff float64
	scoreRatio := float64(score) / 100.0

	for _, poseConf := range allPoseConf {
		if poseConf == nil {
			continue
		}

		if poseConf.PoseGroup == poseGroup && int32(poseType) == poseConf.PoseType {
			poseCoeff = poseConf.A*scoreRatio*scoreRatio + poseConf.B*scoreRatio + poseConf.C
			break
		}
	}

	return poseCoeff
}