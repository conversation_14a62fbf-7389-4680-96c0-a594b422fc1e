package logicHabit

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// 水层系数

// CalcFishWaterLayerCoeff 计算鱼水层的波动系数
func CalcFishWaterLayerCoeff(ctx context.Context, waterLayerList []commonPB.MAP_WATER_LAYER_TYPE, envFishMap map[int64]int64) map[int64]float64 {
	entry := logx.NewLogEntry(ctx)

	if len(waterLayerList) == 0 || len(envFishMap) == 0 {
		entry.Errorf("waterLayerList:%+v or fishList:%+v is empty", waterLayerList, envFishMap)
		return nil
	}

	// 查询所有鱼环境系数配置
	allFishEnvAffConf := cmodel.GetAllFishEnvAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allFishEnvAffConf) <= 0 {
		entry.Errorf("GetAllFishEnvAffinity config is empty")
		return nil
	}

	// 水层环境系数
	allLayerConf := cmodel.GetAllWaterLayerAffinity(consul_config.WithGrpcCtx(ctx))
	if allLayerConf == nil {
		entry.Errorf("GetAllWaterLayerAffinity config is nil")
		return nil
	}

	coeffMap := make(map[int64]float64, len(envFishMap))

	for fishEnvId, fishId := range envFishMap {

		fishEnvAff, envOk := allFishEnvAffConf[fishEnvId]
		if !envOk {
			continue
		}
		
		layerCoeffConf, structOk := allLayerConf[fishEnvAff.LayerId]
		if !structOk {
			continue
		}
		
		coeffMap[fishId] = CalcMaxCoeffForWaterLayer(ctx, waterLayerList, layerCoeffConf.List)
	}

	entry.Debugf("waterLayerList:%+v, envFishMap:%+v, coeffMap:%+v", waterLayerList, envFishMap, coeffMap)

	return coeffMap
}

// CalcMaxCoeffForWaterLayer 计算水层最大系数
func CalcMaxCoeffForWaterLayer(ctx context.Context, waterLayerList []commonPB.MAP_WATER_LAYER_TYPE, layerConfList []cmodel.WaterLayerAffinityList) float64 {
	
	layerSet := make(map[int32]struct{}, len(waterLayerList))
	// 建立水层关系映射表
	for _, layer := range waterLayerList {
		layerSet[int32(layer)] = struct{}{}
	}

	var maxCoeff float64
	// 遍历水层系数配置，找到最大值 
	for _, layerConf := range layerConfList {
		if _, exist := layerSet[layerConf.LayerType]; exist {
			if layerConf.Coeff > maxCoeff {
				maxCoeff = layerConf.Coeff
			}
		}
	}

	return maxCoeff
}