package logicHabit

import (
	"context"
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
)

func TestCalcFishBaitLengthCoeff(t *testing.T) {
	ctx := context.Background()
	baitPerception := 6.4
	envConf := &cmodel.FishEnvAffinity{
		Id:                   1010003,
		MinAdaptLureRatio: 0.2,
		MaxAdaptLureRatio: 0.5,
		UnderLengthDecayCoeff: 1.5,
		OverLengthDecayCoeff: 2,
		MaxAcceptLengthRatio: 0.8,
	}

	releaseConf := &cmodel.FishRelease {
		Id: 3,
		LengthMin: 40,
		LengthMax: 50,
	}
	CalcFishBaitLengthCoeff(ctx, baitPerception, envConf, releaseConf)
}
