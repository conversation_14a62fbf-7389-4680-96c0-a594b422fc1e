package logicHabit

import (
	"context"
	"math"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// 水温系数

// CalcFishWaterTempCoeff 计算鱼水温的波动系数
func CalcFishWaterTempCoeff(ctx context.Context, waterTemp int32, envFishMap map[int64]int64) map[int64]float64 {
	entry := logx.NewLogEntry(ctx)

	if len(envFishMap) == 0 {
		entry.Error("envFishMap is empty")
		return nil
	}

	// 查询所有鱼环境系数配置
	allFishEnvAffConf := cmodel.GetAllFishEnvAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allFishEnvAffConf) <= 0 {
		entry.Errorf("GetAllFishEnvAffinity config is empty")
		return nil
	}

	// 取所有温度波动系数配置
	allTempAffConf := cmodel.GetAllTempAffinity(consul_config.WithGrpcCtx(ctx))
	if allTempAffConf == nil {
		entry.Error("GetAllTempAffinity config is nil")
		return nil
	}

	// 习性相关静态配置
	affGlobal := cmodel.GetEnvAffinityConst(consul_config.WithGrpcCtx(ctx))
	if affGlobal == nil {
		entry.Error("GetEnvCoeffConst config is nil")
		return nil
	}

	coeffMap := make(map[int64]float64, len(envFishMap))

	for fishEnvId, fishId := range envFishMap {
		fishEnvCoeff, envOk := allFishEnvAffConf[fishEnvId]
		if !envOk {
			continue
		}

		tempAffConf, tempOk := allTempAffConf[fishEnvCoeff.TempId]
		if !tempOk {
			continue
		}

		coeffMap[fishId] = CalcTempCoeff(ctx, waterTemp, tempAffConf, affGlobal)
	}

	entry.Debugf("waterTemp:%d, envFishMap:%+v, coeffMap:%+v", waterTemp, envFishMap, coeffMap)

	return coeffMap
}

// CalcTempCoeff 根据温度和配置计算出温度的亲和系数
// 计算方式: coeff = e^-(waterTemp - tempAffConf.TemperatureFav)^2/global.TEMP_TOLERENCE_WIDTH * tempAffConf.TempAffectedRatio^2
func CalcTempCoeff(ctx context.Context, waterTemp int32, tempAffConf *cmodel.TempAffinity, affGlobal *cmodel.EnvAffinityConst) float64 {
	if tempAffConf == nil || affGlobal == nil {
		return 0
	}

	entry := logx.NewLogEntry(ctx)

	// 根据公式计算出温度的亲和系数
	diff := waterTemp - tempAffConf.TemperatureFav
	// 温度乘以10了 这里取掉
	squaredDiff := float64(diff * diff / 100)
	coeff := math.Exp(-squaredDiff / (affGlobal.TempToleranceWidth * tempAffConf.TempAffectedRatio * tempAffConf.TempAffectedRatio))

	// 如果小于指定的亲和系数 则直接返回0
	if coeff < tempAffConf.TempThreshold {
		coeff = 0.0
	}

	entry.Tracef("waterTemp:%d, tempCoeffConf:%+v, squaredDiff:%f coeff:%f", waterTemp, tempAffConf, squaredDiff, coeff)

	return coeff
}
