package logicHabit

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// 时段系数

// CalcFishTimePeriodCoeff 计算时段系数
func CalcFishTimePeriodCoeff(ctx context.Context, gameTime *commonPB.GameTime, envFishMap map[int64]int64) map[int64]float64 {
	entry := logx.NewLogEntry(ctx)

	if len(envFishMap) == 0 || gameTime == nil {
		entry.Error("calc time period coeff error: fishList is empty or gameTime is nil")
		return nil
	}

	// 查询所有鱼环境系数配置
	allFishEnvAffConf := cmodel.GetAllFishEnvAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allFishEnvAffConf) <= 0 {
		entry.Errorf("GetAllFishEnvAffinity config is empty")
		return nil
	}

	// 查询所有时段配置
	allPeriodAffConf := cmodel.GetAllPeriodAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allPeriodAffConf) <= 0 {
		entry.Errorf("GetAllPeriodAffinity config is empty")
		return nil
	}
	
	curPeriodId := GetPeriodForTime(ctx, gameTime)
	coeffMap := make(map[int64]float64, len(envFishMap))
	
	for fishEvnId, fishId := range envFishMap {
		// 计算单个鱼类的时段系数
		fishEnvAffConf, envOk := allFishEnvAffConf[fishEvnId]
		if !envOk {
			continue
		}
		
		for _, periodConf := range allPeriodAffConf {
			if fishEnvAffConf.PeriodCoeffGroup == periodConf.PeriodGroup && curPeriodId == periodConf.PeriodId {
				coeffMap[fishId] = periodConf.PeriodActivityFactor
				break
			}
		}
	}

	entry.Debugf("fish time period gameTime:%+v, envFishMap:%+v, coeffMap:%+v", gameTime, envFishMap, coeffMap)

	return coeffMap
}

func GetPeriodForTime(ctx context.Context, gameTime *commonPB.GameTime) int64 {
	if gameTime == nil {
		return 0
	}

	// 时段配置表
	periodConf := cmodel.GetAllBasicFishPeriod(consul_config.WithGrpcCtx(ctx))
	if periodConf == nil {
		return 0
	}

	for _, period := range periodConf {
		if gameTime.Hour >= period.DayTime.Min && gameTime.Hour < period.DayTime.Max {
			return period.Id
		}
	}

	return 0 
}