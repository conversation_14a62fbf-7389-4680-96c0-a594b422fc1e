package logicConf

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// GetFishBaitTypeCoeffList 获取鱼饵类型波动系数列表
func GetFishBaitTypeCoeffList(ctx context.Context, baitTypeId int32, envFishMap map[int64]int64) map[int64]float64 {
	entry := logx.NewLogEntry(ctx)

	if len(envFishMap) == 0 {
		entry.Errorf("envFishMap is empty")
		return nil
	}

	// 查询所有鱼环境系数配置
	allFishEnvAffConf := cmodel.GetAllFishEnvAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allFishEnvAffConf) <= 0 {
		entry.Errorf("GetAllFishEnvAffinity config is empty")
		return nil
	}

	// 查询所有鱼饵系数配置
	allBaitTypeAffConf := cmodel.GetAllBaitTypeAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allBaitTypeAffConf) <= 0 {
		entry.Errorf("GetAllBaitTypeAffinity config is empty")
		return nil
	}

	coeffMap := make(map[int64]float64, 0)

	for fishEnvId, fishId := range envFishMap {
		fishEnvAffConf, envOk := allFishEnvAffConf[fishEnvId]
		if !envOk {
			continue
		}
		
		for _, baitTypeAff := range allBaitTypeAffConf {
			if fishEnvAffConf.BaitTypeCoeffGroup == baitTypeAff.BaitTypeCoeffGroup && baitTypeAff.BaitSubType == baitTypeId {
				coeffMap[fishId] = baitTypeAff.Coeff
				break
			}
		}
	}

	entry.Debugf("bait type:%d coeffMap:%+v", baitTypeId, coeffMap)

	return coeffMap
} 