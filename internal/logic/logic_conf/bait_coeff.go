package logicConf

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// GetFishBaitCoeffList 获取饵波动系数列表 返回 fishId -> 波动系数
func GetFishBaitCoeffList(ctx context.Context, baitId int64, envFishMap map[int64]int64) map[int64]float64 {
	entry := logx.NewLogEntry(ctx)

	if len(envFishMap) == 0 {
		entry.Errorf("envFishMap is empty")
		return nil
	}

	// 查询所有鱼环境系数配置
	allFishEnvAffConf := cmodel.GetAllFishEnvAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allFishEnvAffConf) <= 0 {
		entry.Errorf("GetAllFishEnvAffinity config is empty")
		return nil
	}

	// 饵波动系数配置
	allBaitAffConf := cmodel.GetAllBaitAffinity(consul_config.WithGrpcCtx(ctx))
	if len(allBaitAffConf) <= 0 {
		entry.Errorf("GetAllBaitAffinity list is empty")
		return nil
	}

	coeffMap := make(map[int64]float64, 0)

	for fishEnvId, fishId := range envFishMap {
		fishEnvAffConf, envOk := allFishEnvAffConf[fishEnvId]
		if !envOk {
			continue
		}

		for _, baitAff := range allBaitAffConf {
			if fishEnvAffConf.BaitCoeffGroup == baitAff.BaitCoeffGroup && baitAff.BaitId == baitId {
				coeffMap[fishId] = baitAff.Coeff
				break
			}
		}
	}

	entry.Debugf("bait:%d, coeffMap: %+v", baitId, coeffMap)

	return coeffMap
}