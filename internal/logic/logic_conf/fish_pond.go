package logicConf

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// GetPondStockConf 查询钓场中的投放配置
func GetPondStockConf(ctx context.Context, pondId int64) *cmodel.FishStock {
	entry := logx.NewLogEntry(ctx)
	pondConf := cmodel.GetFishPondList(pondId, consul_config.WithGrpcCtx(ctx))
	if pondConf == nil {
		entry.Errorf("GetFishPondList pondId:%d config not exist", pondId)
		return nil
	}

	// 取投鱼配置
	stockConf := cmodel.GetFishStock(pondConf.FishStockId, consul_config.WithGrpcCtx(ctx))
	if stockConf == nil {
		entry.Errorf("GetFishStock stockId:%d config not exist", pondConf.FishStockId)
		return nil
	}

	entry.Debugf("pondId:%d stockId:%d conf:%+v", pondId, stockConf.Id, stockConf)

	return stockConf
}

// GetPondFishReleaseInfo 查询钓场的鱼投放信息 fishId:FishRelease*
func GetPondFishReleaseInfo(ctx context.Context, stockId int64) map[int64]*cmodel.FishRelease {
	entry := logx.NewLogEntry(ctx)

	// 投鱼配置信息
	stockReleaseConf := cmodel.GetAllStockRelease(consul_config.WithGrpcCtx(ctx))
	if stockReleaseConf == nil {
		entry.Errorf("stockId:%d StockRelease config not exist", stockId)
		return nil
	}

	// 投鱼信息配置
	fishReleaseConf := cmodel.GetAllFishRelease(consul_config.WithGrpcCtx(ctx))
	if fishReleaseConf == nil {
		entry.Errorf("stockId:%d FishRelease config not exist", stockId)
		return nil
	}

	fishStockMap := make(map[int64]*cmodel.FishRelease)
	for _, fishRelease := range stockReleaseConf {
		if fishRelease.StockId == stockId {
			releaseInfo, ok := fishReleaseConf[fishRelease.ReleaseId]
			if !ok {
				entry.Errorf("stockId:%d FishReleaseId:%d config not exist", stockId, fishRelease.ReleaseId)
				continue
			}

			fishStockMap[fishRelease.FishId] = releaseInfo
		}
	}

	return fishStockMap
}


// GetPondEvnFishMap 查询钓场的环境鱼配置信息 map[fishEnvId]fishId
func GetPondEvnFishMap(ctx context.Context, stockId int64) map[int64]int64 {
	entry := logx.NewLogEntry(ctx)

	// 投鱼配置信息
	stockReleaseConf := cmodel.GetAllStockRelease(consul_config.WithGrpcCtx(ctx))
	if stockReleaseConf == nil {
		entry.Errorf("stockId:%d StockRelease config not exist", stockId)
		return nil
	}

	// 投鱼信息配置
	fishReleaseConf := cmodel.GetAllFishRelease(consul_config.WithGrpcCtx(ctx))
	if fishReleaseConf == nil {
		entry.Errorf("stockId:%d FishRelease config not exist", stockId)
		return nil
	}

	envFishMap := make(map[int64]int64)
	for _, fishRelease := range stockReleaseConf {
		if fishRelease.StockId == stockId {
			_, ok := fishReleaseConf[fishRelease.ReleaseId]
			if !ok {
				entry.Errorf("stockId:%d FishReleaseId:%d config not exist", stockId, fishRelease.ReleaseId)
				continue
			}

			envFishMap[fishRelease.FishEnvId] = fishRelease.FishId
		}
	}

	return envFishMap
}