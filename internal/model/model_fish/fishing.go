package modelFish

import (
	"encoding/json"
)

// Fishing 玩家垂钓数据结构
type Fishing struct {
	PondId int64 `json:"pond_id"` // 水塘id
	RigId  int32 `json:"rig_id"`  // 钓组id
	BaitId int64 `json:"bait_id"` // 鱼饵id

}

// ToJson 转换Fishing为JSON字符串
func (f *Fishing) ToJson() (string, error) {
	jsonBytes, err := json.Marshal(f)
	if err != nil {
		return "", err
	}
	return string(jsonBytes), nil
}

// FromJson 反序列化结构体
func (f *Fishing) FromJson(jsonStr string) error {
	err := json.Unmarshal([]byte(jsonStr), f)
	if err != nil {
		return err
	}
	return nil
}

func (f *Fishing) String() string {
	str, err := f.To<PERSON>()
	if err != nil {
		return ""
	}
	return str
}
