package modelHook

import (
	"context"
	test_init "hook2srv/internal/test"
	"testing"
)

func TestNewFishData(t *testing.T) {
	test_init.InitRedisConsul()
	ctx := context.Background()
	pondId := int64(301020001)
	fishId := int64(1010030)

	for i:= 0; i < 1000; i++ {
		fishData := NewFishData(ctx, pondId, fishId, 0)
		if fishData == nil {
			t.<PERSON><PERSON>("NewFishData should not return nil")
		}
	}
}
