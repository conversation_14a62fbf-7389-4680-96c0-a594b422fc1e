package modelHook

import (
	"encoding/json"
)

type FixHookFish struct {
	PlayerId uint64 `json:"player_id"` // 玩家id
	FishId   int64  `json:"fish_id"`   // 鱼id
	IsRand   bool   `json:"is_rand"`   // 是否随机
}

func NewFixHookFishFromRdsStr(jsonStr string) *FixHookFish {
	// 从json str 中解析
	fixFish := &FixHookFish{}

	err := json.Unmarshal([]byte(jsonStr), fixFish)
	if err != nil {
		return nil
	}

	return fixFish
}

func (f *FixHookFish) ToJsonStr() string {
	if f == nil {
		return ""
	}

	// 格式化成json
	jsonStr, err := json.Marshal(f)
	if err != nil {
		return ""
	}

	return string(jsonStr)
}
