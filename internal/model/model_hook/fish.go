package modelHook

import (
	"context"
	"encoding/json"

	"hook2srv/config"
	"math"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
)

// 玩家钓鱼信息
type FishData struct {
	FishId int64 `json:"fish_id"`                //鱼ID
	Length int32 `json:"length"`                 //鱼长度
	Weight int32 `json:"weight"`                 //鱼重量
	Result commonPB.FISH_RESULT `json:"result"`  //鱼结果
	UnhookValue int32 `json:"unhook_value"`     //脱钩值
}

// NewFishDataFromStr 从字符串初始化玩家
func NewFishDataFromStr(fishStr string) *FishData {
	fishData := &FishData{}
	err := json.Unmarshal([]byte(fishStr), fishData)
	if err != nil {
		return nil
	}

	return fishData
}

// IsValid 判断是否有效
func (f *FishData) IsValid() bool {
	if f == nil {
		return false
	}

	if f.FishId <= 0 || f.Length <= 0 || f.Weight <= 0 {
		return false
	}

	return true
}

// String 输出string 用于日志
func (f *FishData) String() string {
	if f == nil {
		return ""
	}

	jsonBytes, err := json.Marshal(f)
	if err != nil {
		return ""
	}

	return string(jsonBytes)
}

// NewFishData 根据鱼Id 初始化鱼信息
func NewFishData(ctx context.Context, pondId int64, fishId int64, baitId int64) *FishData {
	if fishId <= 0 {
		return nil
	}

	entry := logx.NewLogEntry(ctx)
	fishData := &FishData{}

	// 取钓场中鱼投放信息
	pondConf := cmodel.GetFishPondList(pondId, consul_config.WithGrpcCtx(ctx))
	if pondConf == nil {
		entry.Errorf("GetFishPondList pondId:%d config not exist", pondId)
		return nil
	}

	// 鱼投放配置
	stockRelease := cmodel.GetAllStockRelease(consul_config.WithGrpcCtx(ctx))

	// 投放属性配置id
	var releaseId int64
	// 投放鱼的数据
	for _, release := range stockRelease {
		if release.FishId  == fishId && pondConf.FishStockId == release.StockId {
			releaseId = release.ReleaseId
			break
		}
	}

	releaseConf := cmodel.GetFishRelease(releaseId, consul_config.WithGrpcCtx(ctx))

	if releaseConf == nil {
		entry.Errorf("releaseId:%d FishRelease conf not exist", fishId)
		return nil
	}

	// 使用鱼详细配置信息初始化鱼基础信息
	fishData.Weight = random.Int32Part(releaseConf.WeightMin, releaseConf.WeightMax)

	// 计算长度
	fishData.Length = CalcFishLength(ctx, fishId, fishData.Weight)
	// 赋值真正的鱼id
	fishData.FishId = fishId

	// 计算脱钩值
	fishData.UnhookValue = CalcFishUnhookValue(ctx, fishData.FishId, fishData.Weight, baitId)


	// 按length的比例计算重量(length-最小长度)/(最大长度-最小长度)*(重量最大值-重量最小值) + 重量最小值
	// if fishDetail.FishLen.Min == fishDetail.FishLen.Max {
	// 	fishData.Weight = fishDetail.FishWeight.Min
	// } else {
	// 	fishData.Weight = fishDetail.FishWeight.Min + (fishData.Length-fishDetail.FishLen.Min)*(fishDetail.FishWeight.Max-fishDetail.FishWeight.Min)/(fishDetail.FishLen.Max-fishDetail.FishLen.Min)
	// }

	return fishData
}

// CalcFishLength 计算鱼的长度
// R = random(0.9, 1.1)
// length = math.Pow(w/(MassFactor * randNum), 1.0/VolumeExponent)
func CalcFishLength(ctx context.Context, fishId int64, weight int32) int32 {
	entry := logx.NewLogEntry(ctx)

	// 查询鱼详细信息
	fishDetail := cmodel.GetBasicFishQuality(fishId, consul_config.WithGrpcCtx(ctx))
	if fishDetail == nil {
		entry.Errorf("fishId:%d GetBasicFishQuality config not exist", fishId)
		return 0
	}

	randNum := random.Float64N(config.FISH_WEIGHT_RAND_MAX - config.FISH_WEIGHT_RAND_MIN) + config.FISH_WEIGHT_RAND_MIN

	// 长度
	// L := math.Pow(w/(MassFactor * randNum), 1.0/VolumeExponent)
	length := math.Pow(float64(weight)/(fishDetail.MassFactor*randNum), 1.0/fishDetail.VolumeExponent)

	// randNum := random.Float64N(config.FISH_WEIGHT_RAND_MAX - config.FISH_WEIGHT_RAND_MIN) + config.FISH_WEIGHT_RAND_MIN

	// weight := fishDetail.MassFactor * randNum * math.Pow(float64(length), fishDetail.VolumeExponent)
	
	entry.Debugf("fishId:%d length:%f randNum:%+v weight:%d", fishId, length, randNum, weight)

	return int32(length)
}

// CalcFishUnhookValue 计算鱼的脱钩值
func CalcFishUnhookValue(ctx context.Context, fishId int64, fishWg int32, baitId int64) int32 {
	entry := logx.NewLogEntry(ctx)

	// 查询鱼详细信息
	fishDetail := cmodel.GetBasicFishQuality(fishId, consul_config.WithGrpcCtx(ctx))
	if fishDetail == nil {
		entry.Errorf("fishId:%d GetBasicFishQuality config not exist", fishId)
		return 0
	}

	// 查找鱼属科
	fishSpecies := cmodel.GetBasicFishSpecies(int64(fishDetail.Species), consul_config.WithGrpcCtx(ctx))
	if fishSpecies == nil {
		entry.Errorf("fishSpeciesId:%d GetBasicFishQuality config not exist", fishDetail.Species)
		return 0
	}

	// 查找鱼性格配置
	fishCharacter := cmodel.GetBasicFishCharacter(int64(fishSpecies.Character), consul_config.WithGrpcCtx(ctx))
	if fishCharacter == nil {
		entry.Errorf("fishCharacterId:%d GetBasicFishQuality config not exist", fishSpecies.Character)
		return 0
	}

	// 鱼饵信息
	baitInfo := cmodel.GetItem(baitId, consul_config.WithGrpcCtx(ctx))
	if baitInfo == nil {
		entry.Errorf("GetItem failed, baitId: %d", baitId)
		return 0
	}

	baitLengthFactor := float32(1.0)

	// 这里只用处理拟饵 真饵不用处理
	if baitInfo.ItemType == int32(commonPB.ITEM_TYPE_IT_TACKLE_LURES) {
		// 查询拟饵详细信息
		luresDetailInfo := cmodel.GetLures(baitId, consul_config.WithGrpcCtx(ctx))
		if luresDetailInfo == nil {
			entry.Errorf("GetLures failed, baitId: %d", baitId)
			return 0
		}

		baitLengthFactor = luresDetailInfo.LengthFactor
	} else {
		baitDetailInfo := cmodel.GetBaits(baitId, consul_config.WithGrpcCtx(ctx))
		if baitDetailInfo == nil {
			entry.Errorf("GetBaits failed, baitId: %d", baitId)
			return 0
		}

		baitLengthFactor = baitDetailInfo.LengthFactor
	}

	// 计算脱钩值
	// 脱钩值 = 鱼重量(g) * (1 + 刺鱼通用映射系数 ) * (1 + 饵长度亲和系数)
	// 拟饵使用配置中的长度亲和系数，真饵默认是1
	unhookValue := float32(fishWg) * (1 + fishCharacter.HooksetCommFactor) * (1 + baitLengthFactor)

	entry.Debugf("fishId:%d, baitId:%d, weight:%d, baitLengthFactor:%f, unhookValue:%f", fishId, baitId, fishWg, baitLengthFactor, unhookValue)

	return int32(unhookValue + 0.5)
}

// ToProto 鱼信息转化为proto
func (f *FishData) ToProto(ctx context.Context) *commonPB.FishInfo {
	if f == nil {
		return nil
	}

	fishProto := &commonPB.FishInfo{
		FishId:     f.FishId,
		Length:     f.Length,
		Weight:     f.Weight,
		UnhookValue: f.UnhookValue,
	}

	return fishProto
}
