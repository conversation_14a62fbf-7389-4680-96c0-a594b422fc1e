package modelHook

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
)

// HookData 玩家中鱼信息
type HookData struct {
	PlayerId          uint64 `json:"player_id"`           // 玩家id
	PondId            int64  `json:"pond_id"`             // 池塘id
	RigId             int32  `json:"rig_id"`              // 钓组id
	BaitId            int64  `json:"bait_id"`             // 饵料id
	LastTime          int64  `json:"last_time"`           // 上次请求时间(时间戳毫秒)
	EmptyWeight       int32  `json:"empty_weight"`        // 空鱼权重
	CatchTime         int64  `json:"catch_time"`          // 上次收杆时间(毫秒)
	LeftMsec          int64  `json:"left_msec"`           // 剩余中鱼时间(毫秒)
	FishData          string `json:"fish_data"`           // 鱼数据 使用 FishData
	InvalidWeightTime int64  `json:"invalid_weight_time"` // 无效权重开始时间 (针对实鱼权重为0的情况 秒级时间戳)
	HookId            int64  `json:"hook_id"`             // 钩子id
}

func (h *HookData) ReloadHookData(emptyWeight int32, leftMsec int64) {
	h.InvalidWeightTime = 0
	h.LastTime = timex.Now().UnixMilli()
	h.EmptyWeight = emptyWeight
	h.LeftMsec = leftMsec
}

// NewHookDataFromRdsHash 从redis hash中初始化 hookData
func NewHookDataFromRdsHash(hash map[string]string) *HookData {
	if len(hash) == 0 {
		return nil
	}

	data := &HookData{}
	err := transform.Map2Struct(hash, data)
	if err != nil {
		logrus.Warnf("redis hash convert to hook data failed:%+v", err)
	}
	return data
}

// ToRdsHash 转化为redis hash
func (h HookData) ToRdsHash() map[string]interface{} {
	hash := make(map[string]any)
	err := transform.Struct2Map(h, hash)
	if err != nil {
		logrus.Warnf("hash convert to redis hash failed:%+v", err)
		return nil
	}
	return hash
}

// InsertFishData  钓到鱼
func (h *HookData) InsertFishData(ctx context.Context, fishId int64, baitId int64) {
	if h == nil {
		return
	}

	fishData := NewFishData(ctx, h.PondId, fishId, baitId)
	if fishData != nil {
		h.FishData = fishData.String()
	}
}

// GetFishData 获取鱼数据
func (h *HookData) GetFishData() *FishData {
	if h == nil {
		return nil
	}

	fishStr := h.FishData
	fishData := NewFishDataFromStr(fishStr)

	return fishData
}

func (h *HookData) GetHookBait() *commonPB.HookBait {
	if h == nil {
		return nil
	}

	return &commonPB.HookBait{BaitId: h.BaitId, HookId: h.HookId}
}
