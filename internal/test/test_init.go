package test_init

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/spf13/viper"
)

func InitRedisConsul() {
	viper.SetDefault("consul_addr", "************:8500")
	viper.SetDefault("kafka_url", "************:9092")
	viper.SetDefault("nsqd_addr", "************:4150")

	conf := map[string]string{
		"addr":   "************:6379",
		"passwd": "8888",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		dict_redis.RDBGame:   conf,
		dict_redis.RDBPlayer: conf,
	})
}

func InitSql() {
	db := dict_mysql.MysqlDBGeneral
	conf := map[string]interface{}{
		"addr": "************:3306",
		// "addr":   "localhost:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     db,
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		db: conf,
	})
}
