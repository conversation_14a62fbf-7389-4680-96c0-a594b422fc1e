package tool

import (
	"context"

	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/spf13/viper"
)

const (
	// 默认渠道id
	DEFAULT_CHANNEL_TYPE = 1001
)

// OpenTool 是否开启工具
func OpenTool() bool {
	toolOpen := viper.GetBool("tool_open")
	return toolOpen
}

// GetHookToolConfigPath 获取中鱼工具配置文件路径
func GetHookToolConfigPath() string {
	return viper.GetString("hook_tool_config_path")
}

// NewContext 创建上下文
func NewContext() context.Context {
	channelType := viper.GetInt32("channel_id")
	if channelType <= 0 {
		channelType = DEFAULT_CHANNEL_TYPE
	}

    ctx := interceptor.NewRpcClientCtx(
        interceptor.WithProductId(1),
        interceptor.WithChannelType(channelType),
    )
    return ctx
}
