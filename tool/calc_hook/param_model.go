package calcHook

import (
	"encoding/json"
	"sort"
)

type HookToolParam struct {
	Id         int64   `json:"id"`          // 配置id
	Name       string  `json:"name"`        // 配置名称
	BaitId     int64   `json:"baitId"`      // 鱼饵id
	BaitPose   int32   `json:"baitPose"`    // 鱼饵姿态
	CalcTimes  int32   `json:"calcTimes"`   // 计算次数
	LayerList  []int32 `json:"layerList"`   // 水层列表
	MaxSec     int32 `json:"maxSec"`        // 最大时长
	PondId     int64 `json:"pondId"`        // 钓场id
	PoseScore  int32 `json:"poseScore"`     // 姿态得分
	StructList []int32 `json:"structList"`  // 结构体列表
	WaterTemp  int32   `json:"waterTemp"`   // 水温 * 10
}

func NewHookToolParam(jsonStr string) []*HookToolParam {
	paramMap := make(map[int64]*HookToolParam)

	if err := json.Unmarshal([]byte(jsonStr), &paramMap); err != nil {
		return nil
	}

	// 转化成 list 根据id排序
	paramList := make([]*HookToolParam, 0, len(paramMap))
	for _, v := range paramMap {
		paramList = append(paramList, v)
	}

	// 排序
	sort.Slice(paramList, func(i, j int) bool {
		return paramList[i].Id < paramList[j].Id
	})

	return paramList
}
