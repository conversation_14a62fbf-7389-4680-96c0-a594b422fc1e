package calcHook

import (
	"context"
	modelHook "hook2srv/internal/model/model_hook"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
)

// 计算结果
type hookToolResult struct {
	Id          int     `json:"id"`           // id
	BatchName   string  `json:"batch_name"`   // 批次名称
	Result      bool    `json:"result"`       // 是否中鱼
	FishId      int64   `json:"fish_id"`      // 鱼id
	FishName    string  `json:"fish_name"`    // 鱼名称
	Weight      float64 `json:"weight"`       // 鱼重量
	Length      int32   `json:"length"`       // 鱼长度(cm)
	CalcSec     float64 `json:"time_sec"`     // 计算时间(秒)
	MaxSec      float64 `json:"max_sec"`      // 最大时间(秒)
	WeightValue int32   `json:"weight_value"` // 权重
}

func NewHookToolResult(id int, batchName string, pondId int64, fishId int64, fishQualityMap map[int64]*cmodel.BasicFishQuality, calcSec float64, maxSec float64) *hookToolResult {
	toolRet := &hookToolResult{}
	ctx := context.Background()
	if fishId > 0 {
		fishData := modelHook.NewFishData(ctx, pondId, fishId, 0)
		if fishData != nil {
			fishDetail := fishQualityMap[fishData.FishId]
			if fishDetail != nil {
				toolRet.FishName = fishDetail.Name
			}

			toolRet.FishId = fishData.FishId
			// 重量按kg
			toolRet.Weight = float64(fishData.Weight) / 1000
			toolRet.Length = fishData.Length
			toolRet.Result = true
			maxSec = calcSec
		}
	}

	toolRet.Id = id
	toolRet.BatchName = batchName
	toolRet.MaxSec = maxSec
	toolRet.CalcSec = calcSec

	return toolRet
}
