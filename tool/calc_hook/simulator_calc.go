package calcHook

import (
	"context"
	"fmt"
	"os"
	"time"

	logicConf "hook2srv/internal/logic/logic_conf"
	logicTime "hook2srv/internal/logic/logic_time"
	logicWeight "hook2srv/internal/logic/logic_weight"
	"hook2srv/tool"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"github.com/sirupsen/logrus"
)

func ToolCalcHookDeal() {
	// 开关
	if !tool.OpenTool() {
		return
	}

	ctx := tool.NewContext()

	// 模拟计算中鱼数据
	resultMap := SimulateFishHook(ctx)

	// 导出csv
	ExportHookFishCsv(ctx, resultMap)
}

// SimulateFishHook 模拟计算中鱼数据
func SimulateFishHook(ctx context.Context) []*hookToolResult {
	entry := logrus.WithFields(logrus.Fields{
		"func": "SimulateFishHook",
	})

	paramList := ParseHookToolParam(tool.GetHookToolConfigPath())
	if len(paramList) <= 0 {
		entry.Infof("paramList is nil")
		return nil
	}

	resultMap := make([]*hookToolResult, 0)
	for _, param := range paramList {

		// 转化layerList
		layerList := make([]commonPB.MAP_WATER_LAYER_TYPE, len(param.LayerList))
		for i, layer := range param.LayerList {
			layerList[i] = commonPB.MAP_WATER_LAYER_TYPE(layer)
		}

		// 转化structureList
		structureList := make([]commonPB.UNDER_WATER_STRUCTURE, len(param.StructList))
		for i, structure := range param.StructList {
			structureList[i] = commonPB.UNDER_WATER_STRUCTURE(structure)
		}

		// 初始化习性参数
		habitParam := &commonPB.HookHabitParam{
			WaterTemp:     param.WaterTemp,
			LayerList:     layerList,
			StructureList: structureList,
			BaitPoseInfo: &commonPB.HookBaitTypePose{
				PoseType: commonPB.FISHING_BAIT_TRICK_TYPE(param.BaitPose),
				Score:    param.PoseScore,
			},
		}

		// 计算鱼权重
		fishWeight := logicWeight.CalcPondFishHabitFishWeight(ctx, param.PondId, param.BaitId, habitParam)

		// 获取鱼塘库存配置
		stockConf := logicConf.GetPondStockConf(ctx, param.PondId)
		if stockConf == nil {
			entry.Errorf("GetPondStockConf failed, pondId: %d", param.PondId)
			return nil
		}

		// 总权重
		totalWeight := logicWeight.CalcFishTotalWeight(ctx, fishWeight)

		if totalWeight <= 0 {
			entry.Errorf("totalWeight is less than 0, pondId: %d", param.PondId)
			return nil
		}

		// 鱼表配置
		fishQualityConfMap := cmodel.GetAllBasicFishQuality(consul_config.WithGrpcCtx(ctx))
		if len(fishQualityConfMap) == 0 {
			entry.Errorf("GetAllBasicFishQuality failed, pondId: %d", param.PondId)
			return nil
		}

		emptyWg := stockConf.NoneFishWeight
		lastEmptyWg := stockConf.NoneFishWeight
		lastHook := true

		// 模拟计算中鱼数据
		for i := 0; i < int(param.CalcTimes); i++ {
			// 计算中鱼时间
			hookTime := float64(0)

			// 继承上次空鱼权重
			if lastHook {
				hookTime = logicTime.CalcHookUndulateRatio(ctx, param.PondId, totalWeight, lastEmptyWg)
				logrus.Infof("hookTime1: %f, lastEmptyWg: %d, emptyWg: %d", hookTime, lastEmptyWg, emptyWg)
			} else {
				hookTime = float64(lastEmptyWg) / float64(totalWeight)
				logrus.Infof("hookTime2: %f, lastEmptyWg: %d, emptyWg: %d", hookTime, lastEmptyWg, emptyWg)
			}

			// 重新计算空鱼权重
			lastEmptyWg = int32(hookTime * float64(totalWeight))
			fishId := int64(0)
			// 如果计算出来的中鱼时间小于最大中鱼时间 则可以判断为中鱼
			if hookTime <= float64(param.MaxSec) {
				lastEmptyWg = stockConf.NoneFishWeight
				fishId = random.RandForInt64Weight(fishWeight)
				lastHook = true
			} else {
				lastHook = false
				lastEmptyWg = lastEmptyWg - int32(param.MaxSec*totalWeight)
			}

			// 初始化结果
			toolRet := NewHookToolResult(i, param.Name, param.PondId, fishId, fishQualityConfMap, hookTime, float64(param.MaxSec))

			// 添加到结果
			resultMap = append(resultMap, toolRet)
		}
	}

	// 返回结果
	return resultMap
}

// 解析参数配置
func ParseHookToolParam(fileName string) []*HookToolParam {
	if !tool.OpenTool() {
		return nil
	}

	// 读取 hook_tool_param.json
	jsonStr, err := os.ReadFile(fileName)
	if err != nil {
		logrus.Errorf("ReadFile failed, fileName: %s, err: %v", "hook_tool_param.json", err)
		return nil
	}

	// 解析json
	paramList := NewHookToolParam(string(jsonStr))
	if paramList == nil {
		logrus.Errorf("NewHookToolParam failed, jsonStr: %s", string(jsonStr))
		return nil
	}

	return paramList
}

// ExportHookFishCsv 将导出的列表数据转化成csv
func ExportHookFishCsv(ctx context.Context, resultMap []*hookToolResult) {
	entry := logrus.WithFields(logrus.Fields{
		"func": "ExportHookFishCsv",
	})

	if len(resultMap) == 0 {
		entry.Errorf("resultMap is empty")
		return
	}

	// 文件名 hook_fish_20251015102030 YYYYMMDDHHMMSS
	fileName := fmt.Sprintf("tool_hook_fish_%s.csv", time.Now().Format("20060102150405"))

	// 打开文件
	file, err := os.Create(fileName)
	if err != nil {
		currentDir, _ := os.Getwd() // 获取当前工作目录
		entry.Errorf("Create file failed, fileName: %s, currentDir: %s, err: %v", fileName, currentDir, err)
		return
	}
	defer file.Close()

	// 写入表头
	_, err = file.WriteString("BatchName,序号,结果,鱼名称,重量(kg),长度(cm),计算时间,最大时间\n")
	if err != nil {
		entry.Errorf("Write header failed, err: %v", err)
		return
	}

	// 写入数据
	for i, result := range resultMap {
		//fishId := result.FishId
		resultStr := "中鱼"
		if !result.Result {
			resultStr = "未中鱼"
		}

		// 格式化数据行
		dataLine := fmt.Sprintf("%s,%d,%s,%s,%.3f,%d,%.3f,%.3f\n", result.BatchName, result.Id, resultStr, result.FishName, result.Weight, result.Length, result.CalcSec, result.MaxSec)

		// 写入数据行
		_, err = file.WriteString(dataLine)
		if err != nil {
			entry.Errorf("Write data line failed, index: %d, err: %v", i, err)
			continue
		}
	}

	// 成功
	entry.Infof("CSV file exported successfully, fileName: %s", fileName)
}
